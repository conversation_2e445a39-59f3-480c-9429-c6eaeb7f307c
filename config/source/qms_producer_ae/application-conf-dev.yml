# redis
cy_redis:
  ip: E<PERSON>(Aui1fKQf2iW9dN/VNQKgfY8/BUz9qjRvpnoAwGXfEzJZuqVd45oyBYcDS/VgXWZf)
  port: ENC(EKXV3Uxxnlcc/LQB/IJ2nBUDUq98S+zOxndhyY68546rZICScGdrfvAK1faCllZ1)
  password: 'ujV7c0e672IKBof'
  database: 10

# rabbitMq
cy_mq:
  ip: ************
  port: 5677
  username: mq
  password: mq
# db
cy_db:
  ip: ENC(FI5xxtOs0O5NWkLQ1b9aIrrzWoV89yiLZXSKY1QnYeybTahb9oot2CZK05CC2W7y)
  port: ENC(NU2YN2WvQM4JJAJ7Dm7LQiuRqg6pHgUMk2+5UYQEDxNkGiC9zD4+V7Q3P0F+QMM+)
  username: <PERSON><PERSON>(p8/CtfW6bJHolizYbUHV4aM80hphoovxbMx/7nWaBGwUGCkkAWcZ2/e95a1SYq6y)
  password: ENC(vIrGseZNesDoRzZFJMGG3gcZdZIm+At4LCLQrC/GPMMRtVWa9yf0fZDHT9ksEytj)
  db: ORCLPDB1


# mongodb
cy_mongodb:
  ip: 127.0.0.1
  port: 27017
  username: fox
  password: fox
  db: cy
  authDb: cy

#cy_mongodb:
#  ip: *************
#  port: 27017
#  username: admin
#  password: 123456
#  db: springboot
#  authDb: springboot

cy:
  model:
    swagger2Config: true
    security:
      enable: true
      permission: false
      defaultFilter: true
      gateway: false
      acAllowOrigin: '*'
      acAllowMethods: 'POST, GET, OPTIONS, DELETE,PUT'
      acAllowHeaders: '*'
      permit-all:
        permitUnStatic:
          - "/static/**"
          - "/webjars/**"
          - "/v2/**"
          - "/swagger-resources/**"
          - "/api-docs/**"
          - "/auth/**"
          - "/code/**/**"
          - "/excelUtil/**"
          #- "/cros/**"
          - "/nlttest/add/**"
          - "/**/**"
        permitStatic: [ "/", "/*.html", "/favicon.ico", "/**/*.html", "/**/*.js", "/**/*.css" ]
        logoutSuccessUrl: "/login"
        loginPage: "/login"
        loginfailureUrl: "/login-error.html"

logging:
  level:
    root: info
    com.alibaba.nacos: info
#    root: debug
#    com.alibaba.nacos: debug