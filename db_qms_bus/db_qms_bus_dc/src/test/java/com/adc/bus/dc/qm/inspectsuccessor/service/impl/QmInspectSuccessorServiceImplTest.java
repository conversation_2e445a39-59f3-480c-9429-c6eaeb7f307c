package com.adc.bus.dc.qm.inspectsuccessor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.classesinfo.entity.ClassesInfo;
import com.adc.bus.dc.basic.classesinfo.service.impl.ClassesInfoServiceImpl;
import com.adc.bus.dc.qm.inspectsuccessormuster.entity.dto.InspectSuccessorMusterBaseDTO;
import lombok.CustomLog;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import static org.junit.Assert.*;

@CustomLog
public class QmInspectSuccessorServiceImplTest extends DCApplicationTest {

    @Autowired
    QmInspectSuccessorServiceImpl service;

    @Autowired
    ClassesInfoServiceImpl classesService;

    public Long getCurrentClassesId() {
        List<ClassesInfo> classesInfos = classesService.queryAllByTime(ClassesInfoServiceImpl.formatNowTime(), 0);
        ClassesInfo classesInfo = CollUtil.getFirst(classesInfos);
        log.isNotNull(classesInfo, "没有找到当前班次");
        if (ObjUtil.isNull(classesInfo)) return null;
        return classesInfo.getBusinessId();
    }

    @Test
    public void getMuster() {
        // 高压产线
        List<InspectSuccessorMusterBaseDTO> muster = service.getMuster(1907310828424790016L);
        log.debug("muster: {}", muster);
    }

    @Test
    public void getMuster_prod() {
        List<InspectSuccessorMusterBaseDTO> muster = service.getMuster(1881611178673963008L);
        log.debug("muster: {}", muster);
    }

    @Test
    public void createInspectSuccessor() {
        // 高压产线
        service.createInspectSuccessor(1907310828424790016L, getCurrentClassesId());
    }

    @Test
    public void detailTest() {
        var inspectId = 1933828795727609856L;
        var detail = service.getById(inspectId);
        assertEquals(1, CollUtil.size(detail.getInspectInfoList()));
        assertEquals(1, CollUtil.size(detail.getInspectInfoList().get(0).getScItemInfoList()));
    }

    @Test
    public void detailTest_prod() {
        var inspectId = 1937295857573691392L;
        var detail = service.getById(inspectId);
        assertEquals(4, CollUtil.size(detail.getInspectInfoList()));
        assertEquals(26, CollUtil.size(detail.getInspectInfoList().get(3).getScItemInfoList()));
    }
}