package com.adc.bus.dc.qm.qmmoldswitchsc.service.impl;

import com.adc.bus.dc.DCApplicationTest;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.*;

public class QmMoldSwitchScServiceImplTest extends DCApplicationTest {

    @Resource
    QmMoldSwitchScServiceImpl service;


    @Test
    public void productBindComplete_complete() {
        assertTrue(service.productBindComplete(1915649147902623744L));
    }

    @Test
    public void productBindComplete_noComplete() {
        assertFalse(service.productBindComplete(1914942006250176512L));
    }
}