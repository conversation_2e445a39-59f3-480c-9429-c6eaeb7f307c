package com.adc.bus.dc.outer.mom.bucage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.rcisoft.core.entity.CyIdSnowflakeEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

import static com.adc.core.enums.Constant.TIME_FORMAT;

/**
 * 料笼信息表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "T_MOM_BU_CAGE", autoResultMap = true)
@ApiModel("料笼信息表")
public class MomBuCage extends CyIdSnowflakeEntity<MomBuCage> {

    /**
     * 料笼表ID
     */
    @ApiModelProperty("料笼表ID")
    private String cageId;

    /**
     * 料笼号
     */
    @ApiModelProperty("料笼号")
    private String cageNo;

    /**
     * 料笼状态 0:装笼中 1:封笼 2:已全部出笼
     */
    @ApiModelProperty("料笼状态 0:装笼中 1:封笼 2:已全部出笼")
    private String packageStatus;

    /**
     * 接收区域
     */
    @ApiModelProperty("接收区域")
    private String goWay;

    /**
     * 产线
     */
    @ApiModelProperty("产线")
    private String lineNo;

    /**
     * 物流状态 0:正常 1:隔离中
     */
    @ApiModelProperty("物流状态 0:正常 1:隔离中")
    private String insulateStatus;

    /**
     * 工件类型 缸体、缸盖、低缸体、发动机
     */
    @ApiModelProperty("工件类型 缸体、缸盖、低缸体、发动机")
    private String partType;

    /**
     * 机型编码
     */
    @ApiModelProperty("机型编码")
    private String engineModelCode;

    /**
     * 机种编码
     */
    @ApiModelProperty("机种编码")
    private String engineTypeCode;

    /**
     * 料笼当前所在区域编码
     */
    @ApiModelProperty("料笼当前所在区域编码")
    private String nowAreaCode;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private BigDecimal orderNo;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = TIME_FORMAT)
    @DateTimeFormat(pattern = TIME_FORMAT)
    private Date createdTime;

    /**
     * 最后更新人员
     */
    @ApiModelProperty("最后更新人员")
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    @ApiModelProperty("最后更新时间")
    @JsonFormat(pattern = TIME_FORMAT)
    @DateTimeFormat(pattern = TIME_FORMAT)
    private Date lastUpdatedTime;

    /**
     * 是否可用
     */
    @ApiModelProperty("是否可用")
    private String enabled;

    /**
     * SDP用户ID
     */
    @ApiModelProperty("SDP用户ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @ApiModelProperty("SDP组织ID")
    private String sdpOrgId;

    /**
     * 并发控制字段
     */
    @ApiModelProperty("并发控制字段")
    private String updateControlId;

    /**
     * 料笼类型
     */
    @ApiModelProperty("料笼类型")
    private String cageType;

    /**
     * 下线点位编码
     */
    @ApiModelProperty("下线点位编码")
    private String offlinePoint;

    /**
     * 工件数量
     */
    @ApiModelProperty("工件数量")
    private BigDecimal partQty;

    /**
     * 是否发送立体库
     */
    @ApiModelProperty("是否发送立体库")
    private String isSendAts;

    /**
     * 立体库入库时间
     */
    @ApiModelProperty("立体库入库时间")
    @JsonFormat(pattern = TIME_FORMAT)
    @DateTimeFormat(pattern = TIME_FORMAT)
    private Date atsInstoreDate;

    /**
     * 立体库入库货位
     */
    @ApiModelProperty("立体库入库货位")
    private String atsInstorePlace;

    /**
     * 立体库出库时间
     */
    @ApiModelProperty("立体库出库时间")
    @JsonFormat(pattern = TIME_FORMAT)
    @DateTimeFormat(pattern = TIME_FORMAT)
    private Date atsOutstoreDate;

    /**
     * 二维码前缀
     */
    @ApiModelProperty("二维码前缀")
    private String qrPerfix;

    /**
     * 料笼出库时间
     */
    @ApiModelProperty("料笼出库时间")
    @JsonFormat(pattern = TIME_FORMAT)
    @DateTimeFormat(pattern = TIME_FORMAT)
    private Date outStoreDate;
}
