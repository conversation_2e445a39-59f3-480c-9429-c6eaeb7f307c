import re
import sys
import os

def read_java_enum(java_file_path):
    with open(java_file_path, 'r', encoding='utf-8') as file:
        return file.read()

def extract_class_comments_and_enums(java_content):
    # 正则表达式匹配Java枚举类上面的注释、Java注解和枚举名称
    class_comment_pattern = re.compile(r'(/\*\*((?:.|\n)*?)\*/)(?:\s*@\w+.*?)*\s*(public )?enum (\w+)', re.DOTALL)
    enum_pattern = re.compile(r'(\w+)\("([^"]+)",\s*"([^"]+)"\)')

    # 寻找类注释、Java注解和枚举定义
    match = class_comment_pattern.search(java_content)
    if match:
        class_comments = match.group(1).strip()  # 获取注释内容
        enum_name = match.group(3)  # 获取枚举名称
        enums = enum_pattern.findall(java_content)
        return class_comments, enum_name, enums
    else:
        return None, None, None

def convert_to_ts_enum(class_comments, enums, enum_class_name):
    # 将Java注释转换为TypeScript注释
    ts_comments = class_comments.replace('*/', '').replace('/**', '').strip()
    
    # 创建TypeScript枚举
    ts_enum_content = (
        "// This file is auto-generated from Java enum FormTypeEnum. Do not edit directly.\n"
        f'/**\n {ts_comments}\n */\nexport enum {enum_class_name} {{\n'
    )
    for name, value, annotate in enums:
        ts_enum_content += f'  /** {annotate} */\n  {name} = \'{value}\',\n\n'
    ts_enum_content += "}"
    return ts_enum_content.strip() + "\n"

def write_ts_enum(ts_enum_content, ts_file_path):
    with open(ts_file_path, 'w', encoding='utf-8') as file:
        file.write(ts_enum_content)

def ensure_file_name(path, enum_class_name):
    # 确保ts_file_path包含文件名，如果没有，则添加默认文件名
    if not os.path.basename(path):
        ts_file_path = os.path.join(path, enum_class_name + '.ts')

    # 确保目标路径存在
    os.makedirs(os.path.dirname(path), exist_ok=True)
    return path


def main():
    if len(sys.argv) < 2:
        print("Usage: python script.py <java_enum_path> [<ts_enum_path>]")
        sys.exit(1)

    java_file_path = sys.argv[1]  # Java枚举文件路径
    ts_file_path = sys.argv[2] if len(sys.argv) > 2 else "."  # TypeScript枚举文件路径

    # 从Java枚举文件名中提取枚举类名
    java_file_name = os.path.basename(java_file_path)
    enum_class_name = java_file_name.replace('.java', '')

    # 读取Java枚举文件内容
    java_enum_content = read_java_enum(java_file_path)

    class_comments, enum_name, enums = extract_class_comments_and_enums(java_enum_content)

    # 转换并生成TypeScript枚举内容
    ts_enum_content = convert_to_ts_enum(class_comments, enums, enum_class_name)

    # 确保ts_file_path包含文件名，如果没有，则添加默认文件名
    ts_file_path = ensure_file_name(ts_file_path, enum_class_name)

    # 写入TypeScript枚举文件
    write_ts_enum(ts_enum_content, ts_file_path)

    print(f"TypeScript enum '{enum_class_name}' has been written to {ts_file_path}")

if __name__ == '__main__':
    main()