# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

DB QMS API 是一个基于 Spring Cloud 微服务架构的数据库质量管理系统，采用 Java 17 开发。项目通过模块化设计，将不同业务领域的功能分散到独立的微服务中。

## 构建命令

### Maven 构建
```bash
# 构建所有模块
mvn clean install

# 构建特定模块
mvn clean install -pl db_qms_bus/db_qms_bus_dc

# 跳过测试
mvn clean install -DskipTests

# 运行特定模块的测试
mvn test -pl db_qms_bus/db_qms_bus_dc
```

### 启动应用
```bash
# 启动 DC Bus 服务
cd db_qms_bus/db_qms_bus_dc
mvn spring-boot:run

# 启动 MC Bus 服务
cd db_qms_bus/db_qms_bus_mc
mvn spring-boot:run

# 启动生产者服务
cd db_qms_producer/db_qms_producer_dc
mvn spring-boot:run

# 启动消费者服务
cd db_qms_consumer/db_qms_consumer_dc
mvn spring-boot:run
```

## 系统架构

### 核心模块结构

1. **db_qms_bus** - 业务逻辑核心模块
   - `db_qms_bus_ae` - AE 业务模块
   - `db_qms_bus_dc` - DC 业务模块
   - `db_qms_bus_mc` - MC 业务模块
   - `db_qms_bus_met` - MET 业务模块

2. **db_qms_producer** - 数据生产者模块
   - 负责数据采集和发布
   - 对应不同业务领域的数据生产

3. **db_qms_consumer** - 数据消费者模块
   - 负责数据消费和处理
   - 实现业务逻辑的异步处理

4. **db_qms_common** - 公共模块
   - `db_qms_core` - 核心工具和配置
   - `db_qms_orm` - 数据库访问层
   - `db_qms_util` - 通用工具类
   - `db_qms_workflow` - 工作流组件

### 技术栈特点

- **微服务架构**: 基于 Spring Cloud 2021.0.1
- **服务注册与发现**: Nacos 1.4.1
- **分布式事务**: Seata 1.3.0
- **数据库**: MyBatis-Plus
- **消息队列**: RocketMQ
- **缓存**: Redis
- **Java 版本**: JDK 17

### 关键设计模式

1. **多模块设计**: 每个业务领域独立模块化
2. **配置管理**: 使用 Nacos 进行配置管理，支持多环境配置
3. **事件驱动**: 通过 Producer/Consumer 模式实现异步处理
4. **分层架构**: Controller -> Service -> Repository 标准三层架构

### 配置管理

项目使用 Nacos 进行配置管理，配置文件结构：
- `application-common.yml` - 公共配置
- `application-conf-{env}.yml` - 环境特定配置
- `application-spbt.yml` - 框架配置
- `application-{env}.yml` - 主配置文件

### 开发规范

1. **包结构**: 按功能模块组织，遵循标准 Spring Boot 项目结构
2. **代码风格**: 使用 Lombok 减少样板代码
3. **数据库操作**: 统一使用 MyBatis-Plus
4. **日志管理**: 使用 Logback 配置，支持多环境日志级别

### 环境变量

关键环境变量：
- `SPRING_ACTIVE` - 激活的配置文件 (dev/test/prod)
- `NACOS_NAMESPACE` - Nacos 命名空间
- `NACOS_ADDR` - Nacos 服务地址
- `NACOS_USERNAME/NACOS_PASSWORD` - Nacos 认证信息

### 部署说明

项目支持多种部署方式：
1. 本地开发环境直接运行
2. Docker 容器化部署
3. Kubernetes 集群部署

### 测试策略

- 单元测试位于各模块的 `src/test` 目录
- 集成测试需要启动相关中间件
- 使用 Maven Surefire 插件执行测试

### 重要目录说明

- `config/` - 外部配置文件管理
- `kettle/` - ETL 数据处理脚本
- `sql/` - 数据库初始化脚本
- `memory-bank/` - 项目记忆银行文档
- `.cursor/rules/` - Cursor IDE 规则配置

### 开发注意事项

1. 使用中文注释和文档
2. 遵循现有的代码结构和命名规范
3. 新增功能时优先考虑模块化设计
4. 确保配置的环境兼容性
5. 测试用例需要覆盖主要业务逻辑