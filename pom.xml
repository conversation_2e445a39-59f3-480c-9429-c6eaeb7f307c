<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>db.qms</groupId>
    <artifactId>db_qms_api</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>db_qms_common</module>
        <module>db_qms_bus</module>
        <module>db_qms_consumer</module>
        <module>db_qms_producer</module>
    </modules>

    <developers>
        <developer>
            <name>cy</name>
            <email>xxx.com</email>
        </developer>
    </developers>

    <properties>
        <!--project prop-->
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>

        <!--packages prop -->
        <adc_da.version>3.2.0_WF_A_P_QMS_beta24</adc_da.version>
        <spring.boot.version>2.6.3</spring.boot.version>
        <spring.cloud.version>2021.0.1</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.0.1.0</spring.cloud.alibaba.version>

        <lombok.version>1.18.24</lombok.version>
        <oracle.jdbc.version>19.13.0.0</oracle.jdbc.version>
    </properties>


    <dependencies>
        <!--lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>3.1.7</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <!-- 0. springboot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <!-- 1. adc_da -->
            <dependency>
                <groupId>org.91isoft</groupId>
                <artifactId>91isoft_spbt</artifactId>
                <version>${adc_da.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.afterturn</groupId>
                        <artifactId>easypoi-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.afterturn</groupId>
                        <artifactId>easypoi-annotation</artifactId>
                    </exclusion>
                </exclusions>
                <!--        dependencyManager-exclusion 只起提示作用     -->
            </dependency>
            <!-- 2. spring cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>
            <!-- 3. spring cloud alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>nexus-91isoft</id>
            <name>local private nexus</name>
            <url>http://nexus.91isoft.com:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>nexus-aliyun</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>nexus-91isoft-public</id>
            <name>local private nexus</name>
            <url>http://nexus.91isoft.com:8081/nexus/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-91isoft-third</id>
            <name>third</name>
            <url>http://nexus.91isoft.com:8081/nexus/repository/third/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <!--aliyun-->
        <repository>
            <id>aliyun-repos</id>
            <url>https://maven.aliyun.com/repository/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>nexus-ossrh-snapshots</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <!--Not authorized , ReasonPhrase: Unauthorized-->

    </repositories>

    <pluginRepositories>

        <pluginRepository>
            <id>nexus-91isoft</id>
            <url>http://nexus.91isoft.com:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>nexus-releases</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>

    </pluginRepositories>
</project>
