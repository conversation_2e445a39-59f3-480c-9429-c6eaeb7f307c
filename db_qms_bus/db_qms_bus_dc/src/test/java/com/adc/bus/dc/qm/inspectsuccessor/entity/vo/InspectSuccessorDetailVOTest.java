package com.adc.bus.dc.qm.inspectsuccessor.entity.vo;

import cn.hutool.core.collection.ListUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.inspectiontemplateinfo.enums.InspectionDimensionEnum;
import com.adc.bus.dc.basic.modelmanage.entity.vo.ModelManageOptionVO;
import com.adc.bus.dc.qm.common.entity.dto.InspectInfoDTO;
import com.adc.bus.dc.qm.inspectsuccessor.entity.dto.ScItemInfoWithDimensionsDTO;
import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class InspectSuccessorDetailVOTest extends DCApplicationTest {

    /**
     * 产品绑定完成 关联机型导致完成
     */
    @Test
    public void getInspectProductBindComplete_noRelationModel_model_true() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                        .setDimension(InspectionDimensionEnum.MODEL)
                        .setModelId(1L)
                        .setScItemId(1L)
                        .setNeedInspect(3)
                        .setBindInspectCount(3)))));
        assertTrue(detailVO.getInspectProductBindComplete());
    }
        

    /**
     * 产品绑定完成 关联机型导致完成
     */
    @Test
    public void getInspectProductBindComplete_relationModel_model_true() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                        .setDimension(InspectionDimensionEnum.MODEL)
                        .setModelId(1L)
                        .setScItemId(1L)
                                .setNeedInspect(3)
                                .setBindInspectCount(3),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MODEL)
                                .setModelId(2L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setBindInspectCount(2)))));
        assertTrue(detailVO.getInspectProductBindComplete());
    }

    @Test
    public void getInspectProductBindComplete_relationModel_model_false() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                        .setDimension(InspectionDimensionEnum.MODEL)
                        .setModelId(1L)
                        .setScItemId(1L)
                        .setNeedInspect(3)
                        .setBindInspectCount(2),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MODEL)
                                .setModelId(3L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setBindInspectCount(3)))));
        assertFalse(detailVO.getInspectProductBindComplete());
    }


    /**
     * 产品绑定完成 模具维度 关联机型导致完成
     */
    @Test
    public void getInspectProductBindComplete_relationModel_mold_true() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MOLD)
                                .setModelId(1L)
                                .setMoldId(1L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setBindInspectCount(3),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MOLD)
                                .setModelId(2L)
                                .setMoldId(1L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setBindInspectCount(2)))));
        assertTrue(detailVO.getInspectProductBindComplete());
    }
    /**
     * 产品绑定完成 模具维度 关联机型导致完成
     */
    @Test
    public void getInspectProductBindComplete_relationModel_mold_false() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MOLD)
                                .setModelId(1L)
                                .setMoldId(1L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setBindInspectCount(3),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MOLD)
                                .setModelId(2L)
                                .setMoldId(2L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setBindInspectCount(2)))));
        assertFalse(detailVO.getInspectProductBindComplete());
    }

    @Test
    public void getInspectComplete_model_true() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                        .setDimension(InspectionDimensionEnum.MODEL)
                        .setModelId(1L)
                        .setScItemId(1L)
                        .setNeedInspect(3)
                        .setAlreadyInspect(3),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MODEL)
                                .setModelId(2L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setAlreadyInspect(2)))));
        assertTrue(detailVO.getInspectComplete());
    }

    @Test
    public void getInspectComplete_model_false() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                        .setDimension(InspectionDimensionEnum.MODEL)
                        .setModelId(3L)
                        .setScItemId(1L)
                        .setNeedInspect(3)
                        .setAlreadyInspect(3),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MODEL)
                                .setModelId(2L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setAlreadyInspect(2)))));
        assertFalse(detailVO.getInspectComplete());
    }

    @Test
    public void getInspectComplete_mold_true() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                        .setDimension(InspectionDimensionEnum.MOLD)
                        .setModelId(1L)
                        .setMoldId(1L)
                        .setScItemId(1L)
                        .setNeedInspect(3)
                        .setAlreadyInspect(3),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MOLD)
                                .setModelId(2L)
                                .setMoldId(1L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setAlreadyInspect(2)))));
        assertTrue(detailVO.getInspectComplete());
    }

    @Test
    public void getInspectComplete_mold_false() {
        InspectSuccessorDetailVO detailVO = new InspectSuccessorDetailVO();
        detailVO.setModelMap(ListUtil.toList(
                new ModelManageOptionVO()
                        .setBusinessId(2L)
                        .setIndepententModelId(1L),
                new ModelManageOptionVO()
                        .setBusinessId(4L)
                        .setIndepententModelId(3L)));
        detailVO.setInspectInfoList(ListUtil.toList(new InspectInfoDTO<>(1L, "a",
                ListUtil.toList((ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                        .setDimension(InspectionDimensionEnum.MOLD)
                        .setModelId(1L)
                        .setMoldId(1L)
                        .setScItemId(1L)
                        .setNeedInspect(3)
                        .setAlreadyInspect(3),
                        (ScItemInfoWithDimensionsDTO)new ScItemInfoWithDimensionsDTO()
                                .setDimension(InspectionDimensionEnum.MOLD)
                                .setModelId(2L)
                                .setMoldId(2L)
                                .setScItemId(1L)
                                .setNeedInspect(3)
                                .setAlreadyInspect(2)))));
        assertFalse(detailVO.getInspectComplete());
    }
}