package com.adc.bus.dc.basic.processdevicescanner.service.impl;

import cn.hutool.json.JSONUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.devicename.service.impl.DeviceNameServiceImpl;
import com.adc.bus.dc.basic.processdevicescanner.entity.ProcessDeviceScanner;
import com.adc.bus.dc.basic.processdevicescanner.entity.dto.ScannerWeightDTO;
import com.adc.bus.dc.basic.processdevicescanner.service.ProcessDeviceScannerService;
import com.adc.bus.dc.basic.prolineinfo.entity.ProlineInfo;
import com.adc.bus.dc.basic.prolineinfo.service.ProlineInfoService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;

public class ProcessDeviceScannerServiceImplTest extends DCApplicationTest {

    @Resource
    private ProcessDeviceScannerService scannerService;

    @Resource
    DeviceNameServiceImpl procesDeviceService;

    @Resource
    private ProlineInfoService proLineService;

    @Test
    public void getScannerWeightList() {
        String proLineName = "高压生产线";
        ProlineInfo proLine = proLineService.lambdaQuery().eq(ProlineInfo::getProlineName, proLineName).one();
        List<List<List<ScannerWeightDTO>>> scannerWeightList = scannerService
                .getScannerWeightList(proLine.getBusinessId());
        System.out.println(JSONUtil.toJsonStr(scannerWeightList));
    }

    @Test
    public void scannerIsConsecutive() {
        String proLineName = "YA产线";
        ProlineInfo proLine = proLineService.lambdaQuery().eq(ProlineInfo::getProlineName, proLineName).one();
        Long proLineId = proLine.getBusinessId();
        // 要查询的扫码位置编码
        String scanner01 = "YA-S-01";
        String scanner11 = "YA-S-11";
        String scanner12 = "YA-S-12";
        String scanner21 = "YA-S-21";
        // 查询扫码位置ID
        Long scanner01Id = scannerService.lambdaQuery().eq(ProcessDeviceScanner::getCode, scanner01).one().getBusinessId();
        Long scanner11Id = scannerService.lambdaQuery().eq(ProcessDeviceScanner::getCode, scanner11).one().getBusinessId();
        Long scanner12Id = scannerService.lambdaQuery().eq(ProcessDeviceScanner::getCode, scanner12).one().getBusinessId();
        Long scanner21Id = scannerService.lambdaQuery().eq(ProcessDeviceScanner::getCode, scanner21).one().getBusinessId();
        // 查询扫码位置是否连续
        assertThat(scannerService.scannerIsConsecutive(proLineId, scanner01Id, scanner11Id)).isTrue();
        assertThat(scannerService.scannerIsConsecutive(proLineId, scanner01Id, scanner12Id)).isTrue();
        assertThat(scannerService.scannerIsConsecutive(proLineId, scanner11Id, scanner21Id)).isTrue();
        assertThat(scannerService.scannerIsConsecutive(proLineId, scanner12Id, scanner21Id)).isTrue();
        // 查询扫码位置是否连续
        assertThat(scannerService.scannerIsConsecutive(proLineId, scanner01Id, scanner21Id)).isFalse();
        assertThat(scannerService.scannerIsConsecutive(proLineId, scanner11Id, scanner12Id)).isFalse();
        
    }

    @Test
    public void scannerIsConsecutive2() {
         // 查询扫码位置是否连续
         assertThat(scannerService.scannerIsConsecutive(1881610819113058304L, 1891789741184253952L, 1891792457105145856L)).isTrue();
    }

    @Test
    public void getNextScannerId() {
        Set<Long> outletPosition = procesDeviceService.getOutletPositionByProcessDeviceId(1907380288523337730L);
        ProcessDeviceScanner nextScanner = scannerService.getNextScanner(1932736336830398464L, true, outletPosition, true);
        assertEquals(1907615594187063296L, nextScanner.getBusinessId().longValue());
    }
}