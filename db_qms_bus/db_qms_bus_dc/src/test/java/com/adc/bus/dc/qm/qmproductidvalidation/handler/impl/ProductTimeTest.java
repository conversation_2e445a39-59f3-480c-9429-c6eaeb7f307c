package com.adc.bus.dc.qm.qmproductidvalidation.handler.impl;

import cn.hutool.core.date.DateUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.qm.qmproductidvalidation.entity.dto.CheckProductionIdDTO;
import com.adc.bus.dc.qm.qmproductidvalidation.entity.dto.ProductionIdCheckDatabaseInfoDTO;
import com.adc.bus.dc.qm.qmproductidvalidationresult.entity.dto.ProductionIdValidationResultBaseDTO;
import com.adc.bus.dc.qm.qmproductidvalidationresult.enums.ProductionIdValidationResultEnum;
import org.junit.Test;

import java.util.Date;

import static org.junit.Assert.*;

public class ProductTimeTest extends DCApplicationTest {


    @Test
    public void check() {
        Date productTime = DateUtil.parseDateTime("2025-05-07 23:56:36");
        ProductionIdValidationResultBaseDTO resultBaseDTO = new ProductTime().check(new CheckProductionIdDTO()
                        .setProductionId("DB59B86250508B5060")
                        .setProductionTime(productTime),
                new ProductionIdCheckDatabaseInfoDTO()
                        .setProductionFloatTime(1800));
        assertEquals(ProductionIdValidationResultEnum.CORRECT, resultBaseDTO.getValidationResult());
    }
}