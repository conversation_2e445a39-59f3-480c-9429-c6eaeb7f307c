import json
from datetime import datetime, timedelta
import sys

def generate_data(product_id):
    base_data = {
        "productId": product_id,
        "paramScannerId": "GCSSM",
        "plcId": "R11",
        "cageId": "6",
        "hotModuleSignal": "HOT_MODULE"
    }
    
    # 生成三个数据点
    result = []
    # 获取当前时间作为基础时间
    base_time = datetime.now()
    
    for i in range(3):
        current_data = base_data.copy()
        
        # 设置type (第一个点是4，后面都是3)
        current_data["type"] = "4" if i == 0 else "3"
        
        # 设置扫码位置编号 (与dapCode尾号相同)
        scanner_suffix = str(i + 1)
        current_data["paramScannerId"] = f"GCSSM0{scanner_suffix}"
        
        # 设置时间（每个点间隔5秒）
        current_time = base_time + timedelta(seconds=i*5)
        current_data["throughTime"] = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 设置参数列表
        current_data["paramList"] = [{
            "dapCode": f"DapCode1{scanner_suffix}",
            "dapValue": 10 + i,
            "dapUnit": "anim ullamco Ut",
            "collectTime": current_time.strftime("%Y-%m-%d %H:%M:%S")
        }]
        
        result.append(current_data)
    
    return result

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("错误：必须提供产品ID")
        print("使用方法：python generateCrossDotData.py <产品ID>")
        sys.exit(1)
    
    product_id = sys.argv[1]
    
    # 生成数据并打印
    data_points = generate_data(product_id)
    for i, data in enumerate(data_points, 1):
        print(f"\n数据点 {i}:")
        print(json.dumps(data, indent=4, ensure_ascii=False))
