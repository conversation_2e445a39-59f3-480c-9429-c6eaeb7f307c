package com.adc.bus.dc.basic.classesinfo.entity;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.adc.bus.dc.DCApplicationTest;
import lombok.CustomLog;
import org.junit.Test;

import static org.junit.Assert.*;

@CustomLog
public class ClassesInfoTest extends DCApplicationTest {

    @Test
    public void testEquals() {
        ClassesInfo classesInfoA = JSONUtil.toBean("""
                {"remarks":null,"createBy":"1","createDate":"2025-01-20 22:33:38","updateBy":"6130","updateDate":"2025-05-23 04:14:27","delFlag":"0","flag":"TRUE","businessId":1881590965068890112,"classesName":"早班","classesShortName":"A","deptId":2,"isTwodays":"FALSE","startDate":"07:31","endDate":"16:15","version":null}""", ClassesInfo.class);
        ClassesInfo classesInfoB = JSONUtil.toBean("""
                {"remarks":null,"createBy":"1","createDate":"2025-01-20 22:33:38","updateBy":"6130","updateDate":"2025-05-29 04:14:27","delFlag":"0","flag":"TRUE","businessId":1881590965068890112,"classesName":"早班","classesShortName":"A","deptId":2,"isTwodays":"FALSE","startDate":"07:31","endDate":"16:15","version":null}""", ClassesInfo.class);
        log.debugJson("同类型为{},消息班次{}和最新班次{}, 是否相同: {}",
                classesInfoA.getClassesName(),
                classesInfoA, classesInfoB,
                ObjUtil.equals(classesInfoA, classesInfoB));
        assertNotEquals(classesInfoA, classesInfoB);

        ClassesInfo classesInfoA1 = JSONUtil.toBean("""
                {"remarks":null,"createBy":"1","createDate":"2025-01-20 22:33:38","updateBy":"6130","updateDate":"2025-05-23 04:14:27","delFlag":"0","flag":"TRUE","businessId":1881590965068890112,"classesName":"早班","classesShortName":"A","deptId":2,"isTwodays":"FALSE","startDate":"07:31","endDate":"16:15","version":null}""", ClassesInfo.class);
        ClassesInfo classesInfoB1 = JSONUtil.toBean("""
                {"remarks":null,"createBy":"1","createDate":"2025-01-20 22:33:38","updateBy":"6130","updateDate":"2025-05-23 04:14:27","delFlag":"0","flag":"TRUE","businessId":1881590965068890112,"classesName":"早班","classesShortName":"A","deptId":2,"isTwodays":"FALSE","startDate":"07:31","endDate":"16:15","version":null}""", ClassesInfo.class);
        assertEquals(classesInfoA1, classesInfoB1);

        ClassesInfo classesInfoA2 = JSONUtil.toBean("""
                {"classesName":"早班","classesShortName":"A","deptId":2,"isTwodays":"FALSE","startDate":"07:31","endDate":"16:15","businessId":1881590965068890112,"createBy":"1","createDate":1737412418000,"updateBy":"6130","updateDate":1748495418000,"delFlag":"0","flag":"TRUE"}""", ClassesInfo.class);
        ClassesInfo classesInfoB2 = JSONUtil.toBean("""
                {"classesName":"早班","classesShortName":"A","deptId":2,"isTwodays":"FALSE","startDate":"07:31","endDate":"16:15","businessId":1881590965068890112,"createBy":"1","createDate":1737412418000,"updateBy":"6130","updateDate":1748495418000,"delFlag":"0","flag":"TRUE"}""", ClassesInfo.class);
        assertEquals(classesInfoA2, classesInfoB2);


    }
}