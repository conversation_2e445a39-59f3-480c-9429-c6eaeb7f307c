package ${basePackage!}.service;
<#assign tableEntityName = table.entityName[2..]! />

import ${basePackage!}.entity.${tableEntityName};
import com.adc.bus.dc.common.service.DSService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.rcisoft.core.model.CyPageInfo;

import java.util.List;

/**
 * ${table.tableRemark?upper_case} Service
 */
public interface ${tableEntityName}Service extends DSService<${tableEntityName}> {


    /**
     * 分页查询 ${table.tableRemark?upper_case}
     * @param ${tableEntityName?uncap_first} 查询条件
     * @return 结果
     */
    IPage<${tableEntityName}> findAllByPagination(CyPageInfo<${tableEntityName}> paginationUtility, ${tableEntityName} ${tableEntityName?uncap_first});


    /**
     * 查询list ${table.tableRemark?upper_case}
     * @param ${tableEntityName?uncap_first} 查询条件
     * @return 结果
     */
    List<${tableEntityName}> findAll(${tableEntityName} ${tableEntityName?uncap_first});
}
