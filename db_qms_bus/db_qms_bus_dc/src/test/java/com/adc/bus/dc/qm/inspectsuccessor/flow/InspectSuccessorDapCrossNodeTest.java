package com.adc.bus.dc.qm.inspectsuccessor.flow;

import cn.hutool.core.collection.CollUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.inspectiontemplateinfo.entity.vo.InspectionTemplateInfoDetailVO;
import lombok.CustomLog;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.assertEquals;


@CustomLog
public class InspectSuccessorDapCrossNodeTest extends DCApplicationTest {

    @Autowired
    InspectSuccessorDapCrossNode successorDapCrossNode;

    @Test
    public void generateInspectInfo_prod() {
        // LP1#铸造机
        Long scannerId = 1891787578957627392L;
        // CJJB20250523006
        Long inspectId = 1925829054154670080L;
        Long inspectTemplateId = 1921844792992727040L;
        String productId = "DH6A071250523B0078";
        List<InspectionTemplateInfoDetailVO> info = successorDapCrossNode.generateInspectInfo(scannerId, inspectId, inspectTemplateId, productId);
        assertEquals(1, CollUtil.size(info));
        assertEquals(1, CollUtil.size(CollUtil.getFirst(info).getScItemList()));
    }

    @Test
    public void generateInspectInfo_prod_2() {
        // LP1#铸造机
        Long scannerId = 1891797654908960768L;
        // CJJB20250523006
        Long inspectId = 1925829054154670080L;
        Long inspectTemplateId = 1921844792992727040L;
        String productId = "DH6A071250523A0071";
        List<InspectionTemplateInfoDetailVO> info = successorDapCrossNode.generateInspectInfo(scannerId, inspectId, inspectTemplateId, productId);
        assertEquals(1, CollUtil.size(info));
        assertEquals(2, CollUtil.size(CollUtil.getFirst(info).getScItemList()));
    }
}