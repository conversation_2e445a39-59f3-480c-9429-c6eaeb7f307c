@startuml
|DC|
start
group 批量给产品加初物签
:发起初物单;
:暂存;
repeat:编辑;
:提交审批;
floating note left: 添加初物履历
floating note left: 加产品标签初物且不可出库
:审批;
floating note left: 添加初物履历
if (系长通过) then (是)
    :检纪审批;
    endif
    repeat while (系长审批通过) and (检纪审批通过) is (任意不过就回到编辑，重新走审批) not (yes)
    :审批完成;
    floating note left: 加产品标签初物且可出库
    end group
    group MOM申请料笼出库
    if (料笼中有初物标签且不可出库的产品?) is (yes) then
      :推送消息等待审核;
      else (no)
      :出库流程;
    endif
    end group
|DC|
fork
:MC接收;
:DC更新接收信息;
floating note left: 添加初物履历
:MC拦截;
:DC更新拦截信息;
floating note left: 添加初物履历
:确认产品品质;
floating note left: 添加初物履历
if (合格?) is (yes) then
  :更新初物单下的产品状态为合格;
  :如果产品状态全部合格/报废。更新初物单状态为已完成;
else (no)
  :修改初物单状态为异常审批;
  :添加待检纪审批;
endif
:DC审批管理;
:jianji审批;
floating note left: 添加初物履历
if (通过?) is (yes) then
  :进入到初物处理;
  :选择剩余所有产品状态为空的初物单中的产品提交处理，添加初物履历;;
  :修改初物单状态为异常处理审批;
  :添加待系长审批;
  :添加待jianji审批;
  if (报废?) is (yes) then
      :将选择的初物产品状态改为报废;
      :修改初物单状态为已完成;
      end
    else (流通)
      if (所有产品状态都有值：合格/报废?) is (no) then
         :继续到MC等待品质确认;
      else (yes)
        if (拦截过了配置的所有点;?) is (yes) then
          :修改初物单状态为已完成;
          end
        else (no)
          :继续拦截确认流程;
        endif
      endif
    endif
endif
@enduml