<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.adc.bus.dc.qm.qmprocessqualitylast.dao.QmProcessQualityLastRepository">
    <resultMap id="BaseResultMap" type="com.adc.bus.dc.qm.qmprocessqualitylast.entity.QmProcessQualityLast">
        <id column="BUSINESS_ID" property="businessId"/>

        <result column="CREATE_BY" property="createBy"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_DATE" property="updateDate"/>
        <result column="FLAG" property="flag"/>
        <result column="DEL_FLAG" property="delFlag"/>
        <result column="REMARKS" property="remarks"/>
        <result column="VERSION" property="version"/>
        <result column="PRODUCT_ID" property="productId"/>
        <result column="PRO_PROCESS_ID" property="proProcessId"/>
        <result column="DEVICE_ID" property="deviceId"/>
        <result column="NORMAL" property="normal"/>
        <result column="CURRENT_DATA_ID" property="currentDataId"/>
    </resultMap>

    <!--<cache type="${corePackag!}.util.RedisCache"/>-->

    <resultMap id="DetailResultMap"
               type="com.adc.bus.dc.qm.qmprocessqualitylast.entity.vo.QmProcessQualityLastDetailVO">
        <id column="BUSINESS_ID" property="businessId"/>
        <result column="UPDATE_DATE" property="updateDate"/>
        <result column="PRODUCT_ID" property="productId"/>
        <result column="className" property="className"/>
        <result column="proLineName" property="proLineName"/>
        <result column="mainDeviceName" property="mainDeviceName"/>
        <result column="NORMAL" property="normal"/>
        <result column="CURRENT_DATA_ID" property="currentDataId"/>
        <collection property="mainDeviceList"
                    ofType="com.adc.bus.dc.qm.qmprocessqualitydata.entity.vo.QmProcessQualityDataProcessVO"
                    select="getDataList" column="{lineId=LINE_ID,lastBusinessId=BUSINESS_ID}"/>
    </resultMap>


    <resultMap id="PageResultMap" type="com.adc.bus.dc.qm.qmprocessqualitylast.entity.vo.QmProcessQualityLastPageVO">
        <id column="BUSINESS_ID" property="businessId"/>
        <result column="UPDATE_DATE" property="updateDate"/>
        <result column="PRODUCT_ID" property="productId"/>
        <result column="className" property="className"/>
        <result column="proLineName" property="proLineName"/>
        <result column="mainDeviceName" property="mainDeviceName"/>
        <result column="PLC_ID" property="plcId"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="NORMAL" property="normal"/>
        <result column="CURRENT_DATA_ID" property="currentDataId"/>
        <collection property="dapRecordList" select="getParamList" column="CURRENT_DATA_ID"/>
    </resultMap>


    <resultMap id="RecordResultMap" type="com.adc.bus.dc.qm.qmprocessdaprecord.entity.vo.QmDapRecordVO">
        <id column="BUSINESS_ID" property="businessId"/>
        <result column="QM_PROCESS_QUALITY_DATA_ID" property="dataId"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="DAP_CODE" property="dapCode"/>
        <result column="DAP_VALUE" property="dapValue"/>
        <result column="DAP_UNIT" property="dapUnit"/>
        <result column="NORMAL" property="normal"/>
        <result column="COLLECT_TIME" property="collectTime"/>
        <result column="STANDARD" property="paramStandardValue"/>
        <result column="UNIT" property="paramStandardUnit"/>
        <result column="IS_VALID" property="isValid"/>
        <result column="MIN" property="minParamValue"/>
        <result column="MAX" property="maxParamValue"/>
        <result column="TECH_NAME" property="techName"/>
        <result column="CHECK_RESULT" property="checkResult"/>
    </resultMap>


    <resultMap id="ProcessDeviceMap" type="com.adc.bus.dc.qm.qmprocessdaprecord.entity.vo.QmProcessMainDeviceDetailVO">
        <id column="dataId" property="dataId"/>
        <result column="processDeviceId" property="processDeviceId"/>
        <result column="processDeviceName" property="processDeviceName"/>
        <result column="NORMAL" property="normal"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="DUPLICATE_PASS_REASON" property="duplicatePassReason"
                javaType="com.adc.bus.dc.gw.productthroughpointinfo.enums.SecondThroughReasonEnum$Set"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="VIRTUAL_CAGE_NO" property="virtualCageNo"/>
        <result column="INSPECT_RESULT" property="inspectResult"/>
        <result column="SECOND_HEAT_TREAT" property="secondHeatTreat"/>
        <collection property="dapRecordList" ofType="com.adc.bus.dc.qm.qmprocessdaprecord.entity.vo.QmDapRecordVO"
                    select="getDetailParamList" column="dataId"/>
    </resultMap>


    <select id="getDetailParamList" resultMap="RecordResultMap">
        SELECT dr.BUSINESS_ID,
               dr.CREATE_DATE,
               dr.NORMAL,
               p.NAME           as TECH_NAME,
               dr.DAP_VALUE,
               dr.DAP_UNIT,
               dr.DAP_CODE,
               dr.COLLECT_TIME,
               dr.IS_VALID,
               dr.UNIT,
               dr.STANDARD,
               dr.MAX,
               dr.MIN,
               (CASE
                    WHEN dr.NORMAL = '${@com.adc.bus.dc.qm.qmprocessqualitylast.enums.QmParamNormalEnum@NOTHING}' THEN 1
                    ELSE 0 END) AS CHECK_RESULT
        FROM DC_QM_PROCESS_DAP_RECORD dr
                 LEFT JOIN DC_DEVICE_TECH_INFO p ON p.DAP_DEVICE_TECH_NAME = dr.DAP_CODE and p.DEL_FLAG = '0'
                 left join DC_QM_PROCESS_QUALITY_DATA qd on qd.BUSINESS_ID = dr.QM_PROCESS_QUALITY_DATA_ID
                 left join DC_DEVICE_NAME dn on qd.DEVICE_ID = dn.BUSINESS_ID
                 left join DC_DEVICE_TECH_INFO_STANDARD s
                           on s.DEVICE_TECH_INFO_ID = p.BUSINESS_ID
                               and exists(select 1
                                          from json_table(s.MODEL_ID_LIST, '$[*]' columns (model_id number path '$')) j
                                          where j.model_id = dr.MODEL_ID)
        where dr.del_flag = '0'
          AND QM_PROCESS_QUALITY_DATA_ID = #{dataId}
        ORDER BY dr.create_date, dr.BUSINESS_ID
    </select>


    <select id="getParamList" resultMap="RecordResultMap">
        SELECT dr.BUSINESS_ID,
               dr.QM_PROCESS_QUALITY_DATA_ID,
               dr.CREATE_DATE,
               dr.NORMAL,
               p.NAME as TECH_NAME,
               dr.DAP_VALUE,
               dr.DAP_UNIT,
               dr.DAP_CODE,
               dr.COLLECT_TIME,
               dr.UNIT,
               dr.IS_VALID,
               dr.STANDARD,
               dr.MAX,
               dr.MIN
        FROM DC_QM_PROCESS_DAP_RECORD dr
                 LEFT JOIN DC_DEVICE_TECH_INFO p ON p.DAP_DEVICE_TECH_NAME = dr.DAP_CODE and p.DEL_FLAG = '0'
                 left join DC_DEVICE_TECH_INFO_STANDARD s on s.DEVICE_TECH_INFO_ID = p.BUSINESS_ID
        WHERE dr.del_flag = '0'
          AND QM_PROCESS_QUALITY_DATA_ID = #{currentDataId}
        ORDER BY dr.create_date, dr.BUSINESS_ID
    </select>

    <select id="getParamListByIds" resultMap="RecordResultMap">
        SELECT dr.BUSINESS_ID,
               dr.CREATE_DATE,
               dr.QM_PROCESS_QUALITY_DATA_ID,
               dr.NORMAL,
               p.NAME as TECH_NAME,
               dr.DAP_VALUE,
               dr.DAP_UNIT,
               dr.DAP_CODE,
               dr.COLLECT_TIME,
               dr.IS_VALID,
               dr.UNIT,
               dr.STANDARD,
               dr.MAX,
               dr.MIN
        from DC_QM_PROCESS_DAP_RECORD dr
                 left join DC_DEVICE_TECH_INFO_STANDARD s on s.BUSINESS_ID = dr.DEVICE_TECH_PARAM_ID
                 LEFT JOIN DC_DEVICE_TECH_INFO p ON p.BUSINESS_ID = s.DEVICE_TECH_INFO_ID
        WHERE dr.del_flag = '0'
          AND dr.QM_PROCESS_QUALITY_DATA_ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY dr.create_date, dr.BUSINESS_ID
    </select>

    <sql id="queryQmProcessQualityLastsSql">
        SELECT qq.business_id,
               qq.PRODUCT_ID,
               qq.CURRENT_DATA_ID,
               qq.DEVICE_ID,
               dn.NAME,
               qq.PLC_ID,
               sd.DEPT_NAME   as                                           className,
               l.PROLINE_NAME as                                           proLineName,
               m.NAME         as                                           mainDeviceName,
               qq.NORMAL,
               qq.update_date,
               json_arrayagg(qpqdmd.BUSINESS_ID order by qpqd.CREATE_DATE) PASS_PROCESS_ID_LIST
        FROM dc_qm_process_quality_last qq
                 left join DC_DEVICE_NAME dn on dn.BUSINESS_ID = qq.DEVICE_ID
                 left join DC_MAIN_DEVICE m on m.BUSINESS_ID = qq.PRO_PROCESS_ID
                 left join DC_PROLINE_INFO l on l.BUSINESS_ID = qq.LINE_ID
                 left join SYS_DEPT sd on sd.BUSINESS_ID = l.CLASS_ID
                 left join DC_QM_PROCESS_QUALITY_DATA qpqd on qpqd.DEL_FLAG = '0' and qpqd.LAST_ID = qq.BUSINESS_ID
                 left join DC_MAIN_DEVICE qpqdmd on qpqdmd.DEL_FLAG = '0' and qpqdmd.BUSINESS_ID = qpqd.PRO_PROCESS_ID
        where qq.del_flag = '0'
        <if test="entity.productId != null and entity.productId != ''">
            and qq.PRODUCT_ID like concat(concat('%', #{entity.productId}), '%')
        </if>
        <if test="entity.lineIdList != null and entity.lineIdList.size() != 0">
            and l.BUSINESS_ID in
            <foreach collection="entity.lineIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="entity.mainDeviceIdList != null and entity.mainDeviceIdList.size() != 0">
            and qq.PRO_PROCESS_ID in
            <foreach collection="entity.mainDeviceIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="entity.normal != null and entity.normal != ''">
            and qq.NORMAL = #{entity.normal}
        </if>
        <if test="entity.passProcessId != null">
            and exists(select 1
                       from DC_QM_PROCESS_QUALITY_DATA qpqd
                       where qpqd.DEL_FLAG = '0'
                         and qpqd.LAST_ID = qq.BUSINESS_ID
                         and qpqd.PRO_PROCESS_ID = #{entity.passProcessId})
        </if>
        <if test="entity.updateDateStart != null">
            and trunc(qq.UPDATE_DATE) &gt;= #{entity.updateDateStart}
        </if>
        <if test="entity.updateDateEnd != null">
            and trunc(qq.UPDATE_DATE) &lt;= #{entity.updateDateEnd}
        </if>
        group by qq.business_id,
                 qq.PRODUCT_ID,
                 qq.CURRENT_DATA_ID,
                 qq.DEVICE_ID,
                 dn.NAME,
                 qq.PLC_ID,
                 sd.DEPT_NAME,
                 l.PROLINE_NAME,
                 m.NAME,
                 qq.NORMAL,
                 qq.update_date,
                 qq.CREATE_DATE
        ORDER BY qq.create_date DESC
    </sql>
    <select id="queryQmProcessQualityLasts" resultMap="PageResultMap">
        <include refid="queryQmProcessQualityLastsSql"/>
    </select>

    <resultMap id="PageSimpleResultMap"
               type="com.adc.bus.dc.qm.qmprocessqualitylast.entity.vo.QmProcessQualityLastPageVO">
        <id column="BUSINESS_ID" property="businessId"/>
        <result column="UPDATE_DATE" property="updateDate"/>
        <result column="PRODUCT_ID" property="productId"/>
        <result column="className" property="className"/>
        <result column="proLineName" property="proLineName"/>
        <result column="mainDeviceName" property="mainDeviceName"/>
        <result column="NAME" property="name"/>
        <result column="PLC_ID" property="plcId"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="NORMAL" property="normal"/>
        <result column="CURRENT_DATA_ID" property="currentDataId"/>
        <result column="PASS_PROCESS_ID_LIST" property="passProcessIdList"
                javaType="com.adc.core.entity.ListLong"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <select id="queryQmProcessQualityLastsPaged" resultMap="PageSimpleResultMap">
        <include refid="queryQmProcessQualityLastsSql"/>
    </select>


    <sql id="exportSql">
        SELECT pageInfo.PRODUCT_ID,
               pageInfo.CURRENT_DATA_ID,
               pageInfo.NAME,
               pageInfo.proLineName,
               pageInfo.mainDeviceName,
               pageInfo.NORMAL,
               pageInfo.update_date,
               pageInfo.PASS_PROCESS_ID_LIST,
               COALESCE(dr_cnt.paramCount, 0) AS paramCount
        FROM (
        SELECT qq.PRODUCT_ID,
               qq.CURRENT_DATA_ID,
               qq.DEVICE_ID,
               dn.NAME,
               qq.PLC_ID,
               l.PROLINE_NAME as                                           proLineName,
               m.NAME         as                                           mainDeviceName,
               qq.NORMAL,
               qq.update_date,
               json_arrayagg(qpqdmd.BUSINESS_ID order by qpqd.CREATE_DATE) PASS_PROCESS_ID_LIST
        FROM dc_qm_process_quality_last qq
                 left join DC_DEVICE_NAME dn on dn.BUSINESS_ID = qq.DEVICE_ID
                 left join DC_MAIN_DEVICE m on m.BUSINESS_ID = qq.PRO_PROCESS_ID
                 left join DC_PROLINE_INFO l on l.BUSINESS_ID = qq.LINE_ID
                 left join DC_QM_PROCESS_QUALITY_DATA qpqd on qpqd.DEL_FLAG = '0' and qpqd.LAST_ID = qq.BUSINESS_ID
                 left join DC_MAIN_DEVICE qpqdmd on qpqdmd.DEL_FLAG = '0' and qpqdmd.BUSINESS_ID = qpqd.PRO_PROCESS_ID
        where qq.del_flag = '0'
        <if test="entity.productId != null and entity.productId != ''">
            and qq.PRODUCT_ID like concat(concat('%', #{entity.productId}), '%')
        </if>
        <if test="entity.lineIdList != null and entity.lineIdList.size() != 0">
            and l.BUSINESS_ID in
            <foreach collection="entity.lineIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="entity.mainDeviceIdList != null and entity.mainDeviceIdList.size() != 0">
            and qq.PRO_PROCESS_ID in
            <foreach collection="entity.mainDeviceIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="entity.normal != null and entity.normal != ''">
            and qq.NORMAL = #{entity.normal}
        </if>
        <if test="entity.passProcessId != null">
            and exists(select 1
                       from DC_QM_PROCESS_QUALITY_DATA qpqd
                       where qpqd.DEL_FLAG = '0'
                         and qpqd.LAST_ID = qq.BUSINESS_ID
                         and qpqd.PRO_PROCESS_ID = #{entity.passProcessId})
        </if>
        <if test="entity.updateDateStart != null">
            and trunc(qq.UPDATE_DATE) &gt;= #{entity.updateDateStart}
        </if>
        <if test="entity.updateDateEnd != null">
            and trunc(qq.UPDATE_DATE) &lt;= #{entity.updateDateEnd}
        </if>
        group by qq.PRODUCT_ID,
                 qq.CURRENT_DATA_ID,
                 qq.DEVICE_ID,
                 dn.NAME,
                 qq.PLC_ID,
                 l.PROLINE_NAME,
                 m.NAME,
                 qq.NORMAL,
                 qq.update_date,
                 qq.CREATE_DATE
        ORDER BY qq.create_date DESC
        ) pageInfo
            LEFT JOIN (SELECT QM_PROCESS_QUALITY_DATA_ID,
                              COUNT(BUSINESS_ID) AS paramCount
                       FROM DC_QM_PROCESS_DAP_RECORD
                       WHERE DEL_FLAG = '0'
                       GROUP BY QM_PROCESS_QUALITY_DATA_ID) dr_cnt
                      ON dr_cnt.QM_PROCESS_QUALITY_DATA_ID = pageInfo.CURRENT_DATA_ID
    </sql>
    <resultMap id="exportPageSimpleResultMap"
               type="com.adc.bus.dc.qm.qmprocessqualitylast.entity.vo.QmProcessQualityLastPageVO" extends="PageSimpleResultMap">
        <result column="paramCount" property="paramCount"/>
    </resultMap>
    <select id="exportByCursor"  fetchSize="5000" resultSetType="FORWARD_ONLY" resultMap="exportPageSimpleResultMap">
        <include refid="exportSql"/>
    </select>
    <select id="export" resultMap="exportPageSimpleResultMap">
        <include refid="exportSql"/>
    </select>

    <select id="getDetailById" resultMap="DetailResultMap">
        SELECT qq.business_id,
               qq.PRODUCT_ID,
               qq.LINE_ID,
               qq.CURRENT_DATA_ID,
               sd.DEPT_NAME   as className,
               l.PROLINE_NAME as proLineName,
               m.NAME         as mainDeviceName,
               qq.NORMAL,
               qq.update_date
        FROM dc_qm_process_quality_last qq
                 left join DC_DEVICE dd on dd.BUSINESS_ID = qq.DEVICE_ID
                 left join DC_MAIN_DEVICE m on m.BUSINESS_ID = qq.PRO_PROCESS_ID
                 left join DC_PROLINE_INFO l on l.BUSINESS_ID = qq.LINE_ID
                 left join SYS_DEPT sd on sd.BUSINESS_ID = l.CLASS_ID
        where qq.del_flag = '0'
          and qq.business_id = #{id}
    </select>


    <select id="getDataList"
            resultType="com.adc.bus.dc.qm.qmprocessqualitydata.entity.vo.QmProcessQualityDataProcessVO">
        select BUSINESS_ID,
               MAIN_DEVICE_NAME,
               HAS_DATA,
               CREATE_DATE
        from (select md.BUSINESS_ID,
                     md.NAME                                                                               AS MAIN_DEVICE_NAME,
                     1                                                                                     AS HAS_DATA,
                     pqd.CREATE_DATE,
                     ROW_NUMBER() OVER (PARTITION BY md.BUSINESS_ID,md.NAME ORDER BY pqd.CREATE_DATE DESC) AS rn
              from DC_QM_PROCESS_QUALITY_DATA pqd
                       left join DC_MAIN_DEVICE md on md.BUSINESS_ID = pqd.PRO_PROCESS_ID
              where pqd.DEL_FLAG = '0'
                and pqd.LINE_ID = #{lineId}
                and pqd.LAST_ID = #{lastBusinessId}
              order by pqd.CREATE_DATE)
        where rn = 1
        order by CREATE_DATE
    </select>

    <select id="selectByProcessAndProductId" resultMap="ProcessDeviceMap">
        select distinct pqd.BUSINESS_ID                as dataId,
                        dn.BUSINESS_ID                 as processDeviceId,
                        dn.NAME                        as processDeviceName,
                        pqd.NORMAL,
                        pqd.CREATE_DATE,
                        vc.VIRTUAL_CAGE_NO,
                        vctr.INSPECT_RESULT,
                        vctr.CURRENT_SECOND_HEAT_TREAT as SECOND_HEAT_TREAT,
                        to_nchar(ptpi.DUPLICATE_PASS_REASON) as DUPLICATE_PASS_REASON
        from DC_QM_PROCESS_QUALITY_DATA pqd
                 left join DC_PRODUCT_THROUGH_POINT_INFO ptpi on ptpi.BUSINESS_ID = pqd.THROUGH_POINT_ID
                 left join DC_VIRTUAL_CAGE_THROUGH_RECORD vctr
                           on vctr.DEL_FLAG = '0' and vctr.BUSINESS_ID = pqd.VIRTUAL_CAGE_THROUGH_RECORD_ID
                 left join DC_VIRTUAL_CAGE vc on vc.DEL_FLAG = '0' and vc.BUSINESS_ID = vctr.VIRTUAL_CAGE_ID
                 left join DC_DEVICE_NAME dn on dn.DEL_FLAG = '0' and pqd.DEVICE_ID = dn.BUSINESS_ID
                 left join DC_MAIN_DEVICE pp on pqd.PRO_PROCESS_ID = pp.BUSINESS_ID
        where pp.DEL_FLAG = '0'
          and pqd.PRO_PROCESS_ID = #{processId}
          and pqd.PRODUCT_ID = #{productId}
        order by pqd.CREATE_DATE
    </select>

    <select id="selectDapRecordList" resultType="com.adc.bus.dc.qm.qmprocessdaprecord.entity.vo.QmDapRecordVO">
        SELECT qpdr.DAP_VALUE,
               qpdr.NORMAL,
               qpdr.UNIT     as paramStandardUnit,
               qpdr.STANDARD as paramStandardValue,
               qpdr.MAX      as maxParamValue,
               qpdr.MIN      as minParamValue,
               dti.NAME      as techName,
               qpdr.IS_VALID
        FROM DC_QM_PROCESS_DAP_RECORD qpdr
                 LEFT JOIN DC_DEVICE_TECH_INFO_STANDARD dtis
                           ON dtis.DEL_FLAG = '0' AND dtis.BUSINESS_ID = qpdr.DEVICE_TECH_PARAM_ID
                 LEFT JOIN DC_DEVICE_TECH_INFO dti
                           ON dti.BUSINESS_ID = dtis.DEVICE_TECH_INFO_ID
        <where>
            qpdr.QM_PROCESS_QUALITY_DATA_ID = #{dataId}
            <if test="techName != null and techName != ''">
                AND dti.NAME like concat(concat('%', #{techName}), '%')
            </if>
            <if test="normal != null and normal != ''">
                AND qpdr.NORMAL = #{normal}
            </if>
        </where>
    </select>

    <select id="techList" resultType="com.adc.bus.dc.qm.qmprocessdaprecord.entity.vo.QmDapProcessRecordTechListVO">
        select md.NAME  PROCESS_NAME,
               dn.NAME  PROCESS_DEVICE_NAME,
               pds.NAME SCANNER_NAME,
               dti.NAME TECH_INFO_NAME,
               qpdr.DAP_VALUE,
               qpdr.NORMAL,
               qpdr.UNIT,
               qpdr.IS_VALID,
               qpdr.STANDARD,
               qpdr.MAX,
               qpdr.MIN
        from DC_QM_PROCESS_DAP_RECORD qpdr
                 left join DC_QM_PROCESS_QUALITY_DATA qpqd
                           on qpqd.DEL_FLAG = '0' and qpqd.BUSINESS_ID = qpdr.QM_PROCESS_QUALITY_DATA_ID
                 left join DC_PROCESS_DEVICE_SCANNER pds on pds.DEL_FLAG = '0' and pds.BUSINESS_ID = qpqd.SCANNER_ID
                 left join DC_DEVICE_TECH_INFO_STANDARD dtis
                           on dtis.DEL_FLAG = '0' and dtis.BUSINESS_ID = qpdr.DEVICE_TECH_PARAM_ID
                 left join DC_DEVICE_TECH_INFO dti on dti.DEL_FLAG = '0' and dti.BUSINESS_ID = dtis.DEVICE_TECH_INFO_ID
                 left join DC_DEVICE_TECH dt on dt.DEL_FLAG = '0' and dt.BUSINESS_ID = dti.DEVICE_TECH_ID
                 left join DC_DEVICE_NAME dn on dn.DEL_FLAG = '0' and dn.BUSINESS_ID = dt.PROCESS_DEVICE_ID
                 left join DC_MAIN_DEVICE md on md.DEL_FLAG = '0' and md.BUSINESS_ID = dn.MAIN_DEVICE_ID
        where qpqd.PRODUCT_ID = #{productId,jdbcType=VARCHAR,javaType=string}
    </select>

    <select id="getExportTotal" resultType="int">
        select COUNT(DISTINCT CURRENT_DATA_ID)
        from (<include refid="exportSql"/>)
    </select>


    <!--    <select id="getParamListByProcessId" resultType="com.adc.bus.dc.qmprocessdaprecord.entity.QmProcessDapRecord">-->
    <!--        select * from dc_qm_process_dap_record-->
    <!--        where del_flag = '0'-->
    <!--          and QM_PROCESS_QUILITY_DATA_ID = #{currentDataId} and PRO_PROCESS_ID = #{proProcessId}-->
    <!--        ORDER BY create_date-->
    <!--    </select>-->
</mapper>