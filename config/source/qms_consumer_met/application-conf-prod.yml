cy_redis:
  ip: <PERSON><PERSON>(Vi3qXxG1X/F9TLl6z+hqsBMRpHDkW1rTJceDxd8+S4BO037RYDjXNCKE2yqwzXbJ)
  port: ENC(GcvebRBLDTjpCIafezACbpemwiWfK5ZZoBT3FTKCtJrc5tNGCqTICU8sc3d5TaLy)
  password: ''

cy_mq:
  ip: ************
  port: 5677
  username: mq
  password: mq

cy_db:
  ip: ENC(rvsg3jbEGLMAipCAYaj+QUW+lYrT83+maWdm2OKbYHE9UM1M7EDBxuISEzHKoR81LqeTH6JeXPNPPI4/fK3yFw==)
  port: ENC(JKCmJpgMw5T60JaX0OSUWAF2jSvv5q7U2sdB1MoHNsbpgTyWFhXVcgBzLTiQBkSS)
  username: <PERSON><PERSON>(uyCMpa4vqEzmJ75HKhW4E4gXFJ87ad2LnRgu1zGU8sOW2tM4xYi+OFO0iqWr72fk)
  password: E<PERSON>(gJfpkmx+uTJDtJvrfRhhlaAUcORxN3FuKXQIs8speoHnbA8DgNdO7pilt14Rf7hA)
  db: ENC(MphABHmRjusKLB1keD34QKu2f8TDC+QpPhsnk2UHLrYJUiUjxtYjB9YTiobdmk3r)

# mongodb
cy_mongodb:
  ip: 127.0.0.1
  port: 27017
  username: fox
  password: fox
  db: cy
  authDb: cy

cy:
  model:
    swagger2Config: true
    security:
      enable: true
      permission: false
      defaultFilter: true
      gateway: false
      acAllowOrigin: '*'
      acAllowMethods: 'POST, GET, OPTIONS, DELETE,PUT'
      acAllowHeaders: '*'
      permit-all:
        permitUnStatic:
          - "/static/**"
          - "/webjars/**"
          - "/v2/**"
          - "/swagger-resources/**"
          - "/api-docs/**"
          - "/auth/**"
          - "/code/**/**"
          - "/excelUtil/**"
          #- "/cros/**"
        permitStatic: [ "/", "/*.html", "/favicon.ico", "/**/*.html", "/**/*.js", "/**/*.css" ]
        logoutSuccessUrl: "/login"
        loginPage: "/login"
        loginfailureUrl: "/login-error.html"

logging:
  level:
    root: info
    com.alibaba.nacos: info