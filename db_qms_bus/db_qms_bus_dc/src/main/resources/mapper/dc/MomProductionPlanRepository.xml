<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.adc.bus.dc.basic.mom.momproductionplan.dao.MomProductionPlanRepository">
    <resultMap id="BaseResultMap" type="com.adc.bus.dc.basic.mom.momproductionplan.entity.MomProductionPlan">
        <id column="BUSINESS_ID" jdbcType="DOUBLE" property="businessId"/>

        <result column="PLAN_DATE" jdbcType="DATE" property="planDate"/>
        <result column="MOM_PRO_LINE_NO" jdbcType="VARCHAR" property="momProLineNo"/>
        <result column="SAP_MATERIAL_NO" jdbcType="VARCHAR" property="sapMaterialNo"/>
        <result column="SAP_MATERIAL_NAME" jdbcType="VARCHAR" property="sapMaterialName"/>
        <result column="PLAN_COUNT" jdbcType="DOUBLE" property="planCount"/>
        <result column="SYNC_DATE" jdbcType="DATE" property="syncDate"/>
        <result column="MODEL" jdbcType="VARCHAR" property="model"/>
        <result column="PART_TYPE" jdbcType="VARCHAR" property="partType"/>
        <result column="PRO_LINE" jdbcType="VARCHAR" property="proLine"/>
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName"/>
        <result column="CLASS_NAME" jdbcType="VARCHAR" property="className"/>
        <result column="SYNC_VERSION" jdbcType="DOUBLE" property="syncVersion"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="DATE" property="createDate"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_DATE" jdbcType="DATE" property="updateDate"/>
        <result column="FLAG" jdbcType="VARCHAR" property="flag"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="VISION" jdbcType="DOUBLE" property="vision"/>
    </resultMap>

    <!--<cache type="${corePackag!}.util.RedisCache"/>-->
    <select id="queryDcMomProductionPlans" resultMap="BaseResultMap">
        select * from dc_mom_production_plan
        where 1=1
            and del_flag = '0'
            <if test="momProductionPlan.flag != null and momProductionPlan.flag != ''">
                and flag = #{momProductionPlan.flag}
            </if>
            <if test="momProductionPlan.beginTime != null and momProductionPlan.beginTime != ''">
                and PLAN_DATE &gt;= to_date(#{momProductionPlan.beginTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.endTime != null and momProductionPlan.endTime != ''">
                and PLAN_DATE &lt;= to_date(#{momProductionPlan.endTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.momProLineNo != null and momProductionPlan.momProLineNo != ''">
                and MOM_PRO_LINE_NO like concat(concat('%',#{momProductionPlan.momProLineNo}),'%')
            </if>
            <if test="momProductionPlan.sapMaterialNo != null and momProductionPlan.sapMaterialNo != ''">
                and SAP_MATERIAL_NO like concat(concat('%',#{momProductionPlan.sapMaterialNo}),'%')
            </if>
            <if test="momProductionPlan.sapMaterialName != null and momProductionPlan.sapMaterialName != ''">
                and SAP_MATERIAL_NAME like concat(concat('%',#{momProductionPlan.sapMaterialName}),'%')
            </if>
            <if test="momProductionPlan.planCount != null and momProductionPlan.planCount != ''">
                and PLAN_COUNT = #{momProductionPlan.planCount}
            </if>
            <if test="momProductionPlan.beginTime != null and momProductionPlan.beginTime != ''">
                and SYNC_DATE &gt;= to_date(#{momProductionPlan.beginTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.endTime != null and momProductionPlan.endTime != ''">
                and SYNC_DATE &lt;= to_date(#{momProductionPlan.endTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.model != null and momProductionPlan.model != ''">
                and MODEL like concat(concat('%',#{momProductionPlan.model}),'%')
            </if>
            <if test="momProductionPlan.partType != null and momProductionPlan.partType != ''">
                and PART_TYPE like concat(concat('%',#{momProductionPlan.partType}),'%')
            </if>
            <if test="momProductionPlan.proLine != null and momProductionPlan.proLine != ''">
                and PRO_LINE like concat(concat('%',#{momProductionPlan.proLine}),'%')
            </if>
            <if test="momProductionPlan.deptName != null and momProductionPlan.deptName != ''">
                and DEPT_NAME like concat(concat('%',#{momProductionPlan.deptName}),'%')
            </if>
            <if test="momProductionPlan.className != null and momProductionPlan.className != ''">
                and CLASS_NAME like concat(concat('%',#{momProductionPlan.className}),'%')
            </if>
            <if test="momProductionPlan.syncVersion != null and momProductionPlan.syncVersion != ''">
                and SYNC_VERSION = #{momProductionPlan.syncVersion}
            </if>
            <if test="momProductionPlan.vision != null and momProductionPlan.vision != ''">
                and VISION = #{momProductionPlan.vision}
            </if>
        ORDER BY business_id DESC
    </select>

    <select id="queryDcMomProductionPlansPaged" resultMap="BaseResultMap">
        select * from dc_mom_production_plan
        where 1=1
            and del_flag = '0'
            <if test="momProductionPlan.flag != null and momProductionPlan.flag != ''">
                and flag = #{momProductionPlan.flag}
            </if>
            <if test="momProductionPlan.beginTime != null and momProductionPlan.beginTime != ''">
                and PLAN_DATE &gt;= to_date(#{momProductionPlan.beginTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.endTime != null and momProductionPlan.endTime != ''">
                and PLAN_DATE &lt;= to_date(#{momProductionPlan.endTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.momProLineNo != null and momProductionPlan.momProLineNo != ''">
                and MOM_PRO_LINE_NO like concat(concat('%',#{momProductionPlan.momProLineNo}),'%')
            </if>
            <if test="momProductionPlan.sapMaterialNo != null and momProductionPlan.sapMaterialNo != ''">
                and SAP_MATERIAL_NO like concat(concat('%',#{momProductionPlan.sapMaterialNo}),'%')
            </if>
            <if test="momProductionPlan.sapMaterialName != null and momProductionPlan.sapMaterialName != ''">
                and SAP_MATERIAL_NAME like concat(concat('%',#{momProductionPlan.sapMaterialName}),'%')
            </if>
            <if test="momProductionPlan.planCount != null and momProductionPlan.planCount != ''">
                and PLAN_COUNT = #{momProductionPlan.planCount}
            </if>
            <if test="momProductionPlan.beginTime != null and momProductionPlan.beginTime != ''">
                and SYNC_DATE &gt;= to_date(#{momProductionPlan.beginTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.endTime != null and momProductionPlan.endTime != ''">
                and SYNC_DATE &lt;= to_date(#{momProductionPlan.endTime},'yyyy-mm-dd')
            </if>
            <if test="momProductionPlan.model != null and momProductionPlan.model != ''">
                and MODEL like concat(concat('%',#{momProductionPlan.model}),'%')
            </if>
            <if test="momProductionPlan.partType != null and momProductionPlan.partType != ''">
                and PART_TYPE like concat(concat('%',#{momProductionPlan.partType}),'%')
            </if>
            <if test="momProductionPlan.proLine != null and momProductionPlan.proLine != ''">
                and PRO_LINE like concat(concat('%',#{momProductionPlan.proLine}),'%')
            </if>
            <if test="momProductionPlan.deptName != null and momProductionPlan.deptName != ''">
                and DEPT_NAME like concat(concat('%',#{momProductionPlan.deptName}),'%')
            </if>
            <if test="momProductionPlan.className != null and momProductionPlan.className != ''">
                and CLASS_NAME like concat(concat('%',#{momProductionPlan.className}),'%')
            </if>
            <if test="momProductionPlan.syncVersion != null and momProductionPlan.syncVersion != ''">
                and SYNC_VERSION = #{momProductionPlan.syncVersion}
            </if>
            <if test="momProductionPlan.vision != null and momProductionPlan.vision != ''">
                and VISION = #{momProductionPlan.vision}
            </if>
        ORDER BY business_id DESC
    </select>
</mapper>