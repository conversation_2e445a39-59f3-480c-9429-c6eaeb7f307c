<?xml version="1.0" encoding="UTF-8" ?>

<configuration>

    <springProperty name="LOKI_URL" source="loki.url" />
    <springProperty name="LOKI_ENV" source="loki.env" />
    <springProperty name="SERVICE_NAME" source="spring.application.name" />

    <!-- 增加如下的TLog MDC Listener -->
    <contextListener class="com.yomahub.tlog.core.enhance.logback.TLogLogbackTTLMdcListener"/>
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>%c:%L %white(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) /%X{request_path} %X{tl} %boldMagenta(%logger.%M.%L) - %cyan(%msg%n)</pattern>

            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <!--<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
        </encoder>
        <filter class="com.adc.bus.dc.config.XxlJobLogFilter"/>
        <filter class="com.adc.bus.dc.config.AssertLogFilter"/>
    </appender>

    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>
                %date %-5level [%thread] %X{tl} %logger{43}\(%L\) : %msg%n
            </pattern>
        </encoder>
        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--路径-->
            <fileNamePattern>D:/project/isoft/db-qms/db_qms_api/logs/info.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <!--https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx50601fe1a9207d84&redirect_uri=http://gwf.natapp4.cc/sell/weixin/auth&response_type=code&scope=snsapi_userinfo&state=gwf#wechat_redirect-->

    <appender name="fileErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--
            临界值过滤器，过滤掉低于指定临界值的日志。当日志级别等于或高于临界值时，过滤器返回NEUTRAL；当日志级别低于临界值时，日志会被拒绝
        -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>
                %date %-5level [%thread] %X{tl} %logger{43}\(%L\) : %msg%n
            </pattern>
        </encoder>
        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--路径-->
            <fileNamePattern>D:/project/isoft/db-qms/db_qms_api/logs/error.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <!-- 日志传输到skywalking中的appender，通过qrpc传输 -->
    <appender name="gpc-log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>%white(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{10}.%M.%L) - %cyan(%msg%n)</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="loki-log" class="com.github.loki4j.logback.LokiJavaHttpAppender">
        <url>${LOKI_URL}</url>
        <batchSize>500</batchSize>
        <batchTimeoutMs>10000</batchTimeoutMs>
        <encoder class="com.github.loki4j.logback.JsonEncoder">
            <label>
                <pattern>service_name=${SERVICE_NAME},level=%level,tlog=%X{tl}M,request_path=/%X{request_path},env=${LOKI_ENV}</pattern>
            </label>
            <message>
                <pattern>%yellow([${SERVICE_NAME}]) %green([%thread]) %highlight(%-5level) /%X{request_path} %X{tl} %boldMagenta(%logger#%M:%L) %cyan(%msg %ex%n)
                </pattern>
            </message>
            <sortByTime>true</sortByTime>
        </encoder>
        <filter class="com.adc.bus.dc.config.AssertLogFilter"/>
    </appender>


    <!--指定对应包名 -->
    <logger name="com.minlia" level="DEBUG"/>
    <logger name="org.springframework.data.mybatis" level="DEBUG"/>
    <logger name="org.springframework.aop.aspectj" level="ERROR"/>

    <springProfile name="dev">
        <root level="debug">
            <appender-ref ref="consoleLog"/>
<!--            <appender-ref ref="fileInfoLog"/>-->
<!--            <appender-ref ref="fileErrorLog"/>-->
            <appender-ref ref="gpc-log"/>
<!--            <appender-ref ref="loki-log"/>-->
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="debug">
            <appender-ref ref="consoleLog"/>
<!--            <appender-ref ref="fileInfoLog"/>-->
<!--            <appender-ref ref="fileErrorLog"/>-->
            <appender-ref ref="gpc-log"/>
            <appender-ref ref="loki-log"/>
            <!--<appender-ref ref="logStash" />-->
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="debug">
            <appender-ref ref="consoleLog"/>
<!--            <appender-ref ref="fileInfoLog"/>-->
<!--            <appender-ref ref="fileErrorLog"/>-->
            <appender-ref ref="gpc-log"/>
            <appender-ref ref="loki-log"/>
            <!--<appender-ref ref="logStash" />-->
        </root>
    </springProfile>

</configuration>
