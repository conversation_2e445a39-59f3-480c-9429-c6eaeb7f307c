# 检查是否提供了路径参数
if ($args.Count -lt 1) {
    Write-Host "没有提供路径参数，将使用默认路径。"
    $targetPath = Join-Path (Get-Location) 'src\type'
} else {
    $targetPath = $args[0]

    # 检查提供的路径是否以 'src\type' 结尾
    if (-not ($targetPath -match 'src\\type$')) {
        # 如果没有，添加 'src\type' 到路径末尾
        $targetPath = Join-Path $targetPath 'src\type'
    }
}

python ./convertFormTypeEnum.py ../db_qms_common/db_qms_workflow/src/main/java/com/adc/workflow/sysprocessformtype/enums/FormTypeEnum.java ./FormTypeEnum.ts;
python ./convertFormTypeEnum.py ../db_qms_common/db_qms_workflow/src/main/java/com/adc/workflow/sysprocessinstanceapprove/enums/BusinessTypeEnum.java ./BusinessTypeEnum.ts;

# 确保目标路径存在
if (-not (Test-Path $targetPath)) {
    New-Item -ItemType Directory -Path $targetPath > $null
}

# 移动文件到目标路径
Move-Item -Force "./FormTypeEnum.ts" $targetPath
Move-Item -Force "./BusinessTypeEnum.ts" $targetPath

Write-Host "文件已移动到 $targetPath"