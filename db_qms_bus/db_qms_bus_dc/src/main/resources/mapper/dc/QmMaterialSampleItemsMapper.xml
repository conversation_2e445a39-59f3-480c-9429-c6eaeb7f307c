<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.adc.bus.dc.qm.qmmaterialsampleitems.dao.QmMaterialSampleItemsRepository">
    <resultMap id="BaseResultMap" type="com.adc.bus.dc.qm.qmmaterialsampleitems.entity.QmMaterialSampleItems">
        <id column="BUSINESS_ID" jdbcType="INTEGER" property="businessId"/>
        <result column="CREATE_DATE" jdbcType="DATE" property="createDate"/>
        <result column="UPDATE_DATE" jdbcType="DATE" property="updateDate"/>
        <result column="INSPECTION_PROJECT" jdbcType="VARCHAR" property="inspectionProject"/>
        <result column="RAW_MATERIAL_TYPE" jdbcType="VARCHAR" property="rawMaterialType"/>
        <result column="RAW_MATERIAL_ID" jdbcType="INTEGER" property="rawMaterialId"/>
        <result column="DETECTION_VALUE" jdbcType="VARCHAR" property="detectionValue"/>
        <result column="SAMPLE_INSPECT_RESULT" jdbcType="VARCHAR" property="sampleInspectResult"/>
        <result column="CONFIRMATION_METHOD" jdbcType="VARCHAR" property="confirmationMethod"/>
        <result column="TECHNOLOGY_VALUE" jdbcType="VARCHAR" property="technologyValue"/>
        <result column="NUMERIC_STANDARD" jdbcType="VARCHAR" property="numericStandard"/>
        <result column="STANDARD_SPECIFICATIONS" jdbcType="VARCHAR" property="standardSpecifications"/>
        <result column="UPPER_LIMIT" jdbcType="VARCHAR" property="upperLimit"/>
        <result column="LOWER_LIMIT" jdbcType="VARCHAR" property="lowerLimit"/>
        <result column="IS_STEADY" jdbcType="VARCHAR" property="isSteady"/>
    </resultMap>

    <sql id="queryMaterialSampleItems">
        SELECT
        item.BUSINESS_ID,
        item.INSPECTION_PROJECT,
        item.DETECTION_VALUE,
        item.SAMPLE_INSPECT_RESULT,
        item.CONFIRMATION_METHOD,
        item.TECHNOLOGY_VALUE,
        item.NUMERIC_STANDARD,
        item.INSPECTION_FREQUENCY,
        item.STANDARD_SPECIFICATIONS,
        item.UPPER_LIMIT,
        item.LOWER_LIMIT,
        item.IS_STEADY
        FROM DC_QM_MATERIAL_SAMPLE_ITEMS item
        <where>
            AND del_flag = '0'
            <if test="queryDTO.rawMaterialType !=null">
                and RAW_MATERIAL_TYPE = #{queryDTO.rawMaterialType}
            </if>
            <if test="queryDTO.rawMaterialId !=null and queryDTO.rawMaterialId != '' ">
                and RAW_MATERIAL_ID = #{queryDTO.rawMaterialId}
            </if>
        </where>
        ORDER BY UPDATE_DATE DESC, item.BUSINESS_ID DESC
    </sql>

    <select id="queryDcQmMaterialSampleItems" resultType="com.adc.bus.dc.qm.qmmaterialsampleitems.entity.vo.QmMaterialSampleItemsVO">
        <include refid="queryMaterialSampleItems"/>
    </select>

    <select id="queryDcQmMaterialSampleItemsPaged" resultType="com.adc.bus.dc.qm.qmmaterialsampleitems.entity.vo.QmMaterialSampleItemsVO">
        <include refid="queryMaterialSampleItems"/>
    </select>
</mapper>