<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.adc.bus.dc.qm.qmprocessdaprecord.dao.QmProcessDapRecordRepository">
    <resultMap id="BaseResultMap" type="com.adc.bus.dc.qm.qmprocessdaprecord.entity.QmProcessDapRecord">
        <id column="BUSINESS_ID" jdbcType="DOUBLE" property="businessId"/>

        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="DATE" property="createDate"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_DATE" jdbcType="DATE" property="updateDate"/>
        <result column="FLAG" jdbcType="VARCHAR" property="flag"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="VERSION" jdbcType="DOUBLE" property="version"/>
        <result column="DAP_CODE" jdbcType="VARCHAR" property="dapCode"/>
        <result column="MODEL_ID" jdbcType="DOUBLE" property="modelId"/>
        <result column="DEVICE_ID" jdbcType="DOUBLE" property="deviceId"/>
        <result column="QM_PROCESS_QUALITY_DATA_ID" jdbcType="DOUBLE" property="qmProcessQualityDataId"/>
        <result column="PRO_PROCESS_ID" jdbcType="DOUBLE" property="proProcessId"/>
        <result column="DAP_VALUE" jdbcType="DOUBLE" property="dapValue"/>
        <result column="DAP_UNIT" jdbcType="VARCHAR" property="dapUnit"/>
        <result column="NORMAL" jdbcType="VARCHAR" property="normal"/>
        <result column="NET_STATUS" jdbcType="VARCHAR" property="netStatus"/>
        <result column="COLLECT_FREQUENCY" jdbcType="VARCHAR" property="collectFrequency"/>
        <result column="COLLECT_TYPE" jdbcType="VARCHAR" property="collectType"/>
    </resultMap>

    <!--<cache type="${corePackag!}.util.RedisCache"/>-->
    <select id="queryQmProcessDapRecords" resultMap="BaseResultMap">
        select * from DC_QM_PROCESS_DAP_RECORD
        where 1=1
            and del_flag = '0'
            <if test="qmProcessDapRecord.flag != null and qmProcessDapRecord.flag != ''">
                and flag = #{qmProcessDapRecord.flag}
            </if>
            <if test="qmProcessDapRecord.version != null and qmProcessDapRecord.version != ''">
                and VERSION = #{qmProcessDapRecord.version}
            </if>
            <if test="qmProcessDapRecord.dapCode != null and qmProcessDapRecord.dapCode != ''">
                and DAP_CODE like concat(concat('%',#{qmProcessDapRecord.dapCode}),'%')
            </if>
            <if test="qmProcessDapRecord.modelId != null and qmProcessDapRecord.modelId != ''">
                and MODEL_ID = #{qmProcessDapRecord.modelId}
            </if>
            <if test="qmProcessDapRecord.deviceId != null and qmProcessDapRecord.deviceId != ''">
                and DEVICE_ID = #{qmProcessDapRecord.deviceId}
            </if>
            <if test="qmProcessDapRecord.qmProcessQualityDataId != null and qmProcessDapRecord.qmProcessQualityDataId != ''">
                and QM_PROCESS_QUALITY_DATA_ID = #{qmProcessDapRecord.qmProcessQualityDataId}
            </if>
            <if test="qmProcessDapRecord.proProcessId != null and qmProcessDapRecord.proProcessId != ''">
                and PRO_PROCESS_ID = #{qmProcessDapRecord.proProcessId}
            </if>
            <if test="qmProcessDapRecord.dapValue != null and qmProcessDapRecord.dapValue != ''">
                and DAP_VALUE = #{qmProcessDapRecord.dapValue}
            </if>
            <if test="qmProcessDapRecord.dapUnit != null and qmProcessDapRecord.dapUnit != ''">
                and DAP_UNIT like concat(concat('%',#{qmProcessDapRecord.dapUnit}),'%')
            </if>
            <if test="qmProcessDapRecord.normal != null and qmProcessDapRecord.normal != ''">
                and NORMAL like concat(concat('%',#{qmProcessDapRecord.normal}),'%')
            </if>
            <if test="qmProcessDapRecord.netStatus != null and qmProcessDapRecord.netStatus != ''">
                and NET_STATUS like concat(concat('%',#{qmProcessDapRecord.netStatus}),'%')
            </if>
            <if test="qmProcessDapRecord.collectFrequency != null and qmProcessDapRecord.collectFrequency != ''">
                and COLLECT_FREQUENCY like concat(concat('%',#{qmProcessDapRecord.collectFrequency}),'%')
            </if>
            <if test="qmProcessDapRecord.collectType != null and qmProcessDapRecord.collectType != ''">
                and COLLECT_TYPE like concat(concat('%',#{qmProcessDapRecord.collectType}),'%')
            </if>
        ORDER BY business_id DESC
    </select>

    <select id="queryQmProcessDapRecordsPaged" resultMap="BaseResultMap">
        select * from DC_QM_PROCESS_DAP_RECORD
        where 1=1
            and del_flag = '0'
            <if test="qmProcessDapRecord.flag != null and qmProcessDapRecord.flag != ''">
                and flag = #{qmProcessDapRecord.flag}
            </if>
            <if test="qmProcessDapRecord.version != null and qmProcessDapRecord.version != ''">
                and VERSION = #{qmProcessDapRecord.version}
            </if>
            <if test="qmProcessDapRecord.dapCode != null and qmProcessDapRecord.dapCode != ''">
                and DAP_CODE like concat(concat('%',#{qmProcessDapRecord.dapCode}),'%')
            </if>
            <if test="qmProcessDapRecord.modelId != null and qmProcessDapRecord.modelId != ''">
                and MODEL_ID = #{qmProcessDapRecord.modelId}
            </if>
            <if test="qmProcessDapRecord.deviceId != null and qmProcessDapRecord.deviceId != ''">
                and DEVICE_ID = #{qmProcessDapRecord.deviceId}
            </if>
            <if test="qmProcessDapRecord.qmProcessQualityDataId != null and qmProcessDapRecord.qmProcessQualityDataId != ''">
                and QM_PROCESS_QUALITY_DATA_ID = #{qmProcessDapRecord.qmProcessQualityDataId}
            </if>
            <if test="qmProcessDapRecord.proProcessId != null and qmProcessDapRecord.proProcessId != ''">
                and PRO_PROCESS_ID = #{qmProcessDapRecord.proProcessId}
            </if>
            <if test="qmProcessDapRecord.dapValue != null and qmProcessDapRecord.dapValue != ''">
                and DAP_VALUE = #{qmProcessDapRecord.dapValue}
            </if>
            <if test="qmProcessDapRecord.dapUnit != null and qmProcessDapRecord.dapUnit != ''">
                and DAP_UNIT like concat(concat('%',#{qmProcessDapRecord.dapUnit}),'%')
            </if>
            <if test="qmProcessDapRecord.normal != null and qmProcessDapRecord.normal != ''">
                and NORMAL like concat(concat('%',#{qmProcessDapRecord.normal}),'%')
            </if>
            <if test="qmProcessDapRecord.netStatus != null and qmProcessDapRecord.netStatus != ''">
                and NET_STATUS like concat(concat('%',#{qmProcessDapRecord.netStatus}),'%')
            </if>
            <if test="qmProcessDapRecord.collectFrequency != null and qmProcessDapRecord.collectFrequency != ''">
                and COLLECT_FREQUENCY like concat(concat('%',#{qmProcessDapRecord.collectFrequency}),'%')
            </if>
            <if test="qmProcessDapRecord.collectType != null and qmProcessDapRecord.collectType != ''">
                and COLLECT_TYPE like concat(concat('%',#{qmProcessDapRecord.collectType}),'%')
            </if>
        ORDER BY business_id DESC
    </select>


    <select id="selectErrorInfoById" resultType="com.adc.bus.dc.ex.exareainterceptdetail.entity.vo.DeviceTechErrorDTO">
        select dti.NAME        TECH_NAME,
               pdr.DAP_VALUE   ACTUAL,
               pdr.STANDARD,
               pdr.MAX,
               pdr.MIN,
               pdr.UNIT,
               pdr.IS_VALID,
               pdr.CREATE_DATE EX_TIME
        from DC_QM_PROCESS_DAP_RECORD pdr
                 left join DC_DEVICE_TECH_INFO_STANDARD dtis on pdr.DEVICE_TECH_PARAM_ID = dtis.BUSINESS_ID
                 left join DC_DEVICE_TECH_INFO dti on dti.BUSINESS_ID = dtis.DEVICE_TECH_INFO_ID
        where pdr.BUSINESS_ID = #{businessId}
    </select>

    <select id="selectDapRecordByDeviceTechParamId" resultMap="BaseResultMap">
        select dr.DAP_VALUE from DC_QM_PROCESS_DAP_RECORD dr
        left join DC_SC_ITEM_JUDGE_POSITION jp on dr.DEVICE_TECH_PARAM_ID = jp.DEVICE_TECH_PARAM_ID
            left join DC_QM_PROCESS_QUALITY_DATA qpd on dr.QM_PROCESS_QUALITY_DATA_ID = qpd.BUSINESS_ID
            left join DC_QM_PROCESS_QUALITY_LAST qpl on qpd.BUSINESS_ID = qpl.BUSINESS_ID
        where dr.DEVICE_TECH_PARAM_ID = #{deviceTechParamId} and qpd.PRODUCT_ID = #{productId}
            order by qpl.CREATE_DATE desc
        fetch first 1 row only
    </select>

    <select id="selectByProductIds" resultMap="BaseResultMap">
        select r.*,d.PRODUCT_ID
        from DC_QM_PROCESS_DAP_RECORD r
                 left join DC_QM_PROCESS_QUALITY_DATA d on d.DEL_FLAG = 0 and d.BUSINESS_ID = r.QM_PROCESS_QUALITY_DATA_ID
        where r.DEL_FLAG = 0
          and d.PRODUCT_ID in
            <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
    </select>

    <select id="selectTechBaseInfo"
            resultType="com.adc.bus.dc.qm.qmprocessdaprecord.entity.vo.DapTechBaseInfoDTO">
        select dti.NAME TECH_INFO_NAME,
               qpdr.DAP_VALUE,
               qpdr.NORMAL,
               qpdr.STANDARD,
               qpdr.MAX,
               qpdr.MIN,
               qpdr.UNIT,
               qpdr.IS_VALID
        from DC_QM_PROCESS_DAP_RECORD qpdr
                 left join DC_DEVICE_TECH_INFO_STANDARD dtis on qpdr.DEVICE_TECH_PARAM_ID = dtis.BUSINESS_ID
                 left join DC_DEVICE_TECH_INFO dti on dti.BUSINESS_ID = dtis.DEVICE_TECH_INFO_ID
                 left join DC_QM_PROCESS_QUALITY_DATA qpd on qpd.BUSINESS_ID = qpdr.QM_PROCESS_QUALITY_DATA_ID
        <where>
            <if test="throughId != null">
                qpd.THROUGH_POINT_ID = #{throughId}
            </if>
            <if test="qmProcessQualityDataId != null">
                and qpdr.QM_PROCESS_QUALITY_DATA_ID = #{qmProcessQualityDataId}
            </if>
        </where>
        order by qpdr.UPDATE_DATE desc, qpdr.BUSINESS_ID desc
    </select>
</mapper>