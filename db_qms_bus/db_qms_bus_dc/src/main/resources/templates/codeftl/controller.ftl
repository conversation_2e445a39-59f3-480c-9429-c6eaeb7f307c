package ${basePackage!}.controller;
<#assign tableEntityName = table.entityName[2..]! />

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.rcisoft.core.anno.CyOpeLogAnno;
import org.rcisoft.core.operlog.enums.CyLogTypeEnum;
import org.rcisoft.core.util.CyEpExcelUtil;
import org.springframework.validation.BindingResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.adc.core.util.ResultUtil;
import com.adc.core.util.PageUtil;
import com.adc.util.write.ExportUtil;
import com.adc.core.entity.R;
import org.rcisoft.core.model.CyPersistModel;
import org.rcisoft.core.controller.CyPaginationController;
import org.rcisoft.core.model.CyGridModel;
import org.rcisoft.core.exception.CyServiceException;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.skywalking.apm.toolkit.trace.Tag;
import org.apache.skywalking.apm.toolkit.trace.Trace;


import ${basePackage!}.entity.${tableEntityName};
import ${basePackage!}.service.${tableEntityName}Service;

import java.util.List;

/**
 * ${table.tableRemark?upper_case} Controller
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/${tableEntityName?lower_case}")
public class ${tableEntityName}Controller extends CyPaginationController<${tableEntityName}> {

    <#if (table.tableRemark == "")>
        <#assign tableName = (tableEntityName)>
    <#else>
        <#assign tableName = (table.tableRemark)>
    </#if>

    private final ${tableEntityName}Service service;
    private final ExportUtil<${tableEntityName}> exportUtil;

    //@PreAuthorize("@cyPerm.hasPerm('sys:${tableEntityName?uncap_first}:add')")
    @CyOpeLogAnno(title = "${tableName!}管理-新增${tableName!}", businessType = CyLogTypeEnum.INSERT)
    @ApiOperation(value="添加${tableName!}", notes="添加${tableName!}")
    @PostMapping(value = "/add")
    @Trace
    @Tag(key = "${tableEntityName?uncap_first}", value = "arg[0]")
    @Tag(key = "result", value = "returnedObj")
    public R<Boolean> add(@Valid ${tableEntityName} ${tableEntityName?uncap_first}, BindingResult bindingResult) {
        return ResultUtil.result(service.save(${tableEntityName?uncap_first}));
    }

    //@PreAuthorize("@cyPerm.hasPerm('sys:${tableEntityName?uncap_first}:delete')")
    @CyOpeLogAnno(title = "${tableName!}管理-删除${tableName!}", businessType = CyLogTypeEnum.DELETE)
    @ApiOperation(value="删除${tableName!}", notes="删除${tableName!}")
    @ApiImplicitParams({@ApiImplicitParam(name = "businessId", value = "businessId", required = true, dataType = "varchar")})
    @DeleteMapping("/delete/{businessId}")
    @Trace
    @Tag(key = "businessId", value = "arg[0]")
    @Tag(key = "result", value = "returnedObj")
    public R<Boolean> delete(@PathVariable Long businessId) {
        return ResultUtil.result(service.removeById(businessId));
    }

    //@PreAuthorize("@cyPerm.hasPerm('sys:${tableEntityName?uncap_first}:update')")
    @CyOpeLogAnno(title = "${tableName!}管理-修改${tableName!}", businessType = CyLogTypeEnum.UPDATE)
    @ApiOperation(value="修改${tableName!}", notes="修改${tableName!}")
    @PutMapping("/update")
    @Trace
    @Tag(key = "${tableEntityName?uncap_first}", value = "arg[0]")
    @Tag(key = "result", value = "returnedObj")
    public R<Boolean> update(@Valid ${tableEntityName} ${tableEntityName?uncap_first}, BindingResult bindingResult) {
        return ResultUtil.result(service.updateById(${tableEntityName?uncap_first}));
    }

    //@PreAuthorize("@cyPerm.hasPerm('sys:${tableEntityName?uncap_first}:query')")
    @CyOpeLogAnno(title = "${tableName!}管理-查询${tableName!}", businessType = CyLogTypeEnum.QUERY)
    @ApiOperation(value="查询单一${tableName!}", notes="查询单一${tableName!}")
    @ApiImplicitParams({@ApiImplicitParam(name = "businessId", value = "businessId", required = true, dataType = "varchar")})
    @GetMapping("/detail/{businessId}")
    @Trace
    @Tag(key = "businessId", value = "arg[0]")
    @Tag(key = "result", value = "returnedObj")
    public R<${tableEntityName}> detail(@PathVariable Long businessId) {
        return ResultUtil.success(service.getById(businessId));
    }

    //@PreAuthorize("@cyPerm.hasPerm('sys:${tableEntityName?uncap_first}:list')")
    @CyOpeLogAnno(title = "${tableName!}管理-查询${tableName!}", businessType = CyLogTypeEnum.QUERY)
    @ApiOperation(value="查询${tableName!}集合", notes="查询${tableName!}集合")
    @GetMapping(value = "/list-all")
    @Trace
    @Tag(key = "${tableEntityName?uncap_first}", value = "arg[0]")
    @Tag(key = "result", value = "returnedObj")
    public R<List<${tableEntityName}>> listAll(${tableEntityName} ${tableEntityName?uncap_first}) {
        return ResultUtil.success(service.findAll(${tableEntityName?uncap_first}));
    }

    //@PreAuthorize("@cyPerm.hasPerm('sys:${tableEntityName?uncap_first}:list')")
    @CyOpeLogAnno(title = "${tableName!}管理-查询${tableName!}", businessType = CyLogTypeEnum.QUERY)
    @ApiOperation(value="分页查询${tableName!}集合", notes="分页查询${tableName!}集合")
    @GetMapping(value = "/list")
    @Trace
    @Tag(key = "${tableEntityName?uncap_first}", value = "arg[0]")
    @Tag(key = "result", value = "returnedObj")
    public TableInfo<${tableEntityName}> listByPagination(${tableEntityName} ${tableEntityName?uncap_first}) {
        return ResultUtil.page(service.findAllByPagination(PageUtil.getPageInfo(request), ${tableEntityName?uncap_first}));
    }

    @CyOpeLogAnno(title = "${tableName!}管理-查询${tableName!}", businessType = CyLogTypeEnum.EXPORT)
    @ApiOperation(value = "导出${tableName!}信息", notes = "导出${tableName!}信息")
    @GetMapping(value = "/export")
    @Trace
    @Tag(key = "${tableEntityName?uncap_first}", value = "arg[0]")
    @Tag(key = "result", value = "returnedObj")
    public void out${tableEntityName}(${tableEntityName} ${tableEntityName?uncap_first}) {
        exportUtil.export(service.findAll(${tableEntityName?uncap_first}), "${tableName}", ${tableEntityName}.class, response);
    }
}
