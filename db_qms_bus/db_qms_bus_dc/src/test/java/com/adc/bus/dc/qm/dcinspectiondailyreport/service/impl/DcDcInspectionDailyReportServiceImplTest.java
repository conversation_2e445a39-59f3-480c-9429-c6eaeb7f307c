package com.adc.bus.dc.qm.dcinspectiondailyreport.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.classesinfo.entity.ClassesInfo;
import com.adc.bus.dc.basic.classesinfo.service.ClassesInfoService;
import com.adc.bus.dc.basic.classesinfo.service.impl.ClassesInfoServiceImpl;
import com.adc.bus.dc.qm.dcinspectiondailyreport.service.DcInspectionDailyReportService;
import lombok.CustomLog;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@CustomLog
class DcDcInspectionDailyReportServiceImplTest extends DCApplicationTest {

    @Autowired
    DcInspectionDailyReportService reportService;

    @Autowired
    ClassesInfoService classesInfoService;

    @Test
    void generateAndSaveDailyReport() {
        List<ClassesInfo> classesInfos = classesInfoService.queryAllByTime(ClassesInfoServiceImpl.formatNowTime(), 0);
        ClassesInfo classesInfo = CollUtil.getFirst(classesInfos);
        log.isNotNull(classesInfo, "没有找到当前班次");
        if (ObjUtil.isNull(classesInfo)) return;
        reportService.generateAndSaveDailyReport(classesInfo.getBusinessId());
    }

    @Test
    void generateAndSaveDailyReport_test_A() {
        // 测试环境 早班ID
        long classesId = 1907310359942004736L;
        reportService.generateAndSaveDailyReport(classesId);
    }
}