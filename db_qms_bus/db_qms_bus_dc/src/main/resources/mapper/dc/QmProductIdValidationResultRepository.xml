<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.adc.bus.dc.qm.qmproductidvalidationresult.dao.QmProductIdValidationResultRepository">

    <select id="queryProductionIdValidationResultByValidationId"
            resultType="com.adc.bus.dc.qm.qmproductidvalidationresult.entity.dto.ProductionIdValidationResultBaseDTO">
        select pivr.PART_NAME,
               pivr.ACTUAL_CONTENT,
               pivr.CORRECT_CONTENT,
               pivr.VALIDATION_RESULT
        from DC_QM_PRODUCT_ID_VALIDATION_RESULT pivr
        where pivr.DEL_FLAG = '0'
          and pivr.PRODUCT_ID_VALIDATION_ID = #{validationId}
        order by pivr.SORT_ORDER
    </select>
</mapper>