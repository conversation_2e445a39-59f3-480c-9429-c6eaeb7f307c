package com.adc.bus.dc.basic.inspectiontemplate.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.inspectiontemplate.entity.dto.InspectionTemplateQueryDTO;
import com.adc.bus.dc.basic.inspectiontemplate.entity.vo.InspectionTemplateListVO;
import com.adc.bus.dc.basic.inspectiontemplate.enums.InspectTypeEnum;
import lombok.CustomLog;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.Assert.assertTrue;

@CustomLog
public class InspectionTemplateServiceImplTest extends DCApplicationTest {
    
    @Resource
    InspectionTemplateServiceImpl service;

    @Test
    public void findAll() {
        List<InspectionTemplateListVO> list = service.findAll(new InspectionTemplateQueryDTO()
                .setInspectType(InspectTypeEnum.DEVICE_FAIL)
                .setIpmsDeviceIds(ListUtil.toList("7137")));
        assertTrue(CollUtil.isNotEmpty(list));
    }
}