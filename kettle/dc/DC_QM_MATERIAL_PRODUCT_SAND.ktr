<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>DC_QM_MATERIAL_PRODUCT_SAND</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2025/05/17 15:53:59.378</created_date>
    <modified_user>-</modified_user>
    <modified_date>2025/05/17 15:53:59.378</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>MOM</name>
    <server>//103.131.168.155</server>
    <type>ORACLE</type>
    <access>Native</access>
    <database>/MOM</database>
    <port>11521</port>
    <username>mom</username>
    <password>Encrypted 2be98afc86aa7f2e4cb79ce10be9fa0d7</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>11521</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STRICT_NUMBER_38_INTERPRETATION</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>TARGET</name>
    <server>103.131.168.155</server>
    <type>ORACLE</type>
    <access>Native</access>
    <database>/ORCLPDB1</database>
    <port>11521</port>
    <username>SKT1</username>
    <password>Encrypted 2be98afc825ca8381bc2fea79c4ab85e3</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>11521</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STRICT_NUMBER_38_INTERPRETATION</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>MOM造型砂绑定产品信息</from>
      <to>MOM造型砂绑定产品信息 根据MOM造型砂唯一字段升序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>MOM造型砂绑定产品信息 根据MOM造型砂唯一字段升序</from>
      <to>根据MOM造型砂ID进行连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>QMS造型砂信息</from>
      <to>QMS造型砂信息 按照MOM造型砂唯一字段升序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>QMS造型砂信息 按照MOM造型砂唯一字段升序</from>
      <to>根据MOM造型砂ID进行连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>基础信息</from>
      <to>时间 </to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>时间 </from>
      <to>DC_QM_MATERIAL_PRODUCT</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>根据MOM造型砂ID进行连接</from>
      <to>设置ID</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>设置ID</from>
      <to>基础信息</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>DC_QM_MATERIAL_PRODUCT</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>TARGET</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema>SKT1</schema>
      <table>DC_QM_MATERIAL_PRODUCT</table>
      <key>
        <name>KID</name>
        <field>MOM_KID</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>SAND_ID</name>
        <field>MOM_MATERIAL_ID</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>VIN_ENGINE_NO</name>
        <field>PRODUCT_ID</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>BUSINESS_ID</name>
        <rename>BUSINESS_ID</rename>
        <update>N</update>
      </value>
      <value>
        <name>CREATE_BY</name>
        <rename>CREATE_BY</rename>
        <update>N</update>
      </value>
      <value>
        <name>UPDATE_BY</name>
        <rename>UPDATE_BY</rename>
        <update>Y</update>
      </value>
      <value>
        <name>FLAG</name>
        <rename>FLAG</rename>
        <update>N</update>
      </value>
      <value>
        <name>DEL_FLAG</name>
        <rename>DEL_FLAG</rename>
        <update>N</update>
      </value>
      <value>
        <name>RAW_MATERIAL_TYPE</name>
        <rename>RAW_MATERIAL_TYPE</rename>
        <update>N</update>
      </value>
      <value>
        <name>CREATE_DATE</name>
        <rename>CREATE_DATE</rename>
        <update>N</update>
      </value>
      <value>
        <name>UPDATE_DATE</name>
        <rename>UPDATE_DATE</rename>
        <update>Y</update>
      </value>
      <value>
        <name>MOM_KID</name>
        <rename>KID</rename>
        <update>N</update>
      </value>
      <value>
        <name>MOM_MATERIAL_ID</name>
        <rename>SAND_ID</rename>
        <update>N</update>
      </value>
      <value>
        <name>PRODUCT_ID</name>
        <rename>VIN_ENGINE_NO</rename>
        <update>N</update>
      </value>
      <value>
        <name>RAW_MATERIAL_ID</name>
        <rename>QMS_SAND_ID</rename>
        <update>Y</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1768</xloc>
      <yloc>513</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>MOM造型砂绑定产品信息</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>MOM</connection>
    <sql>select sb.KID,
       sb.VIN_ENGINE_NO,
       sfd.BATCH_NO,
       sb.SAND_CATEGORY,
       sfd.SUPPLIER_CODE,
       sfd.MATERIAL_CODE,
       sfd.BATCH_NO || ':' || sb.SAND_CATEGORY || ':' || sfd.SUPPLIER_CODE || ':' || sfd.MATERIAL_CODE SAND_ID
from DHEC_MOM_MOLD.T_MATERIAL_BU_PART_SAND_BATCH sb
         left join DHEC_MOM_MOLD.T_MATERIAL_BU_FS_TRANSFER_RECORD st on st.ENABLED = '1' and st.KID = sb.TRANSFER_RECORD_ID
         left join DHEC_MOM_MOLD.T_MATERIAL_BU_FS_FEEDING_RECORD sf on sf.ENABLED = '1' and sf.FEEDING_BATCH_NO = st.FEEDING_BATCH_NO
         left join DHEC_MOM_MOLD.T_MATERIAL_BU_FS_FEEDING_RECORD_DETAIL sfd on sfd.ENABLED = '1' and sfd.REF_ID = sf.KID
where sf.ENABLED = '1'
  and st.KID is not null
  and sb.KID is not null
  and sfd.KID is not null
  and ('${MOM_DATA_LAST_SYNC_TIME}' is null
    or sb.LAST_UPDATED_TIME >= to_date('${MOM_DATA_LAST_SYNC_TIME}', 'yyyy/MM/dd HH24:mi:ss'))</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>Y</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>KID</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>MOM造型砂绑定产品信息</origin>
        <comments>KID</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>VIN_ENGINE_NO</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>MOM造型砂绑定产品信息</origin>
        <comments>VIN_ENGINE_NO</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>BATCH_NO</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>MOM造型砂绑定产品信息</origin>
        <comments>BATCH_NO</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>SAND_CATEGORY</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>MOM造型砂绑定产品信息</origin>
        <comments>SAND_CATEGORY</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>SUPPLIER_CODE</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>MOM造型砂绑定产品信息</origin>
        <comments>SUPPLIER_CODE</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>MATERIAL_CODE</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>MOM造型砂绑定产品信息</origin>
        <comments>MATERIAL_CODE</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>SAND_ID</name>
        <length>203</length>
        <precision>-1</precision>
        <origin>MOM造型砂绑定产品信息</origin>
        <comments>SAND_ID</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>400</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>MOM造型砂绑定产品信息 根据MOM造型砂唯一字段升序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>SAND_ID</name>
        <ascending>Y</ascending>
        <case_sensitive>Y</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>728</xloc>
      <yloc>401</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>QMS造型砂信息</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>TARGET</connection>
    <sql>select BUSINESS_ID                                                                QMS_SAND_ID,
       BATCH_NUMBER                                                               QMS_BATCH_NO,
       TYPE                                                                       QMS_SAND_CATEGORY,
       SUPPLIER_CODE                                                              QMS_SUPPLIER_CODE,
       MODEL_NUMBER                                                               QMS_MATERIAL_CODE,
       BATCH_NUMBER || ':' || TYPE || ':' || SUPPLIER_CODE || ':' || MODEL_NUMBER MOM_SAND_ID
from DC_QM_MATERIAL_SAND
where DEL_FLAG = '0'</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>Y</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>QMS_SAND_ID</name>
        <length>32</length>
        <precision>0</precision>
        <origin>QMS造型砂信息</origin>
        <comments>QMS_SAND_ID</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>QMS_BATCH_NO</name>
        <length>255</length>
        <precision>-1</precision>
        <origin>QMS造型砂信息</origin>
        <comments>QMS_BATCH_NO</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>QMS_SAND_CATEGORY</name>
        <length>100</length>
        <precision>-1</precision>
        <origin>QMS造型砂信息</origin>
        <comments>QMS_SAND_CATEGORY</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>QMS_SUPPLIER_CODE</name>
        <length>255</length>
        <precision>-1</precision>
        <origin>QMS造型砂信息</origin>
        <comments>QMS_SUPPLIER_CODE</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>QMS_MATERIAL_CODE</name>
        <length>255</length>
        <precision>-1</precision>
        <origin>QMS造型砂信息</origin>
        <comments>QMS_MATERIAL_CODE</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>MOM_SAND_ID</name>
        <length>868</length>
        <precision>-1</precision>
        <origin>QMS造型砂信息</origin>
        <comments>MOM_SAND_ID</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>592</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>QMS造型砂信息 按照MOM造型砂唯一字段升序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>MOM_SAND_ID</name>
        <ascending>Y</ascending>
        <case_sensitive>Y</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>728</xloc>
      <yloc>593</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>基础信息</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>CREATE_BY</name>
        <type>BigNumber</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>1</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>UPDATE_BY</name>
        <type>BigNumber</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>1</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>FLAG</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>TRUE</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>DEL_FLAG</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>0</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>RAW_MATERIAL_TYPE</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>SAND</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1400</xloc>
      <yloc>513</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>时间 </name>
    <type>SystemInfo</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>CREATE_DATE</name>
        <type>system date (fixed)</type>
      </field>
      <field>
        <name>UPDATE_DATE</name>
        <type>system date (fixed)</type>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1560</xloc>
      <yloc>513</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>根据MOM造型砂ID进行连接</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>LEFT OUTER</join_type>
    <step1>MOM造型砂绑定产品信息 根据MOM造型砂唯一字段升序</step1>
    <step2>QMS造型砂信息 按照MOM造型砂唯一字段升序</step2>
    <keys_1>
      <key>SAND_ID</key>
    </keys_1>
    <keys_2>
      <key>MOM_SAND_ID</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1016</xloc>
      <yloc>513</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>设置ID</name>
    <type>UserDefinedJavaClass</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <definitions>
      <definition>
        <class_type>TRANSFORM_CLASS</class_type>
        <class_name>Processor</class_name>
        <class_source>import cn.hutool.core.lang.Singleton;
import cn.hutool.core.lang.Snowflake;
public boolean processRow(StepMetaInterface smi, StepDataInterface sdi) throws KettleException {
  if (first) {
    first = false;

    /* TODO: Your code here. (Using info fields)

    FieldHelper infoField = get(Fields.Info, "info_field_name");

    RowSet infoStream = findInfoRowSet("info_stream_tag");

    Object[] infoRow = null;

    int infoRowCount = 0;

    // Read all rows from info step before calling getRow() method, which returns first row from any
    // input rowset. As rowMeta for info and input steps varies getRow() can lead to errors.
    while((infoRow = getRowFrom(infoStream)) != null){

      // do something with info data
      infoRowCount++;
    }
    */
  }

  Object[] r = getRow();

  if (r == null) {
    setOutputDone();
    return false;
  }

  // It is always safest to call createOutputRow() to ensure that your output row's Object[] is large
  // enough to handle any new fields you are creating in this step.
  r = createOutputRow(r, data.outputRowMeta.size());

  /* TODO: Your code here. (See Sample)

  // Get the value from an input field
  String foobar = get(Fields.In, "a_fieldname").getString(r);

  foobar += "bar";
    
  // Set a value in a new output field
  get(Fields.Out, "output_fieldname").setValue(r, foobar);

  */

  get(Fields.Out, "BUSINESS_ID").setValue(r, ((Snowflake)Singleton.get(Snowflake.class, 0L, 0L)).nextId());

  // Send the row on to the next step.
  putRow(data.outputRowMeta, r);

  return true;
}
</class_source>
      </definition>
    </definitions>
    <fields>
      <field>
        <field_name>BUSINESS_ID</field_name>
        <field_type>Integer</field_type>
        <field_length>32</field_length>
        <field_precision>-1</field_precision>
      </field>
    </fields>
    <clear_result_fields>N</clear_result_fields>
    <info_steps/>
    <target_steps/>
    <usage_parameters/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1240</xloc>
      <yloc>513</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
