<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.adc.bus.dc.basic.ipms.ipmsproductionprocess.dao.IpmsProductionProcessRepository">
    <resultMap id="BaseResultMap" type="com.adc.bus.dc.basic.ipms.ipmsproductionprocess.entity.IpmsProductionProcess">
        <id column="PROCESS_ID" jdbcType="DOUBLE" property="processId"/>

        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="DATE" property="createDate"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_DATE" jdbcType="DATE" property="updateDate"/>
        <result column="FLAG" jdbcType="VARCHAR" property="flag"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="VERSION" jdbcType="DOUBLE" property="version"/>
        <result column="LINE_ID" jdbcType="DOUBLE" property="lineId"/>
        <result column="PROCESS_NAME" jdbcType="VARCHAR" property="processName"/>
        <result column="PROCESS_NO" jdbcType="VARCHAR" property="processNo"/>
        <result column="LOCKED_STATUS" jdbcType="VARCHAR" property="lockedStatus"/>
    </resultMap>

    <select id="queryIpmsProductionProcesss" resultMap="BaseResultMap">
        select *
        from dc_ipms_production_process
        where 1 = 1
          and del_flag = '0'
        <if test="entity.flag != null and entity.flag != ''">
            and flag = #{entity.flag}
        </if>
        <if test="entity.processId != null and entity.processId != ''">
            and PROCESS_ID = #{entity.processId}
        </if>
        <if test="entity.version != null and entity.version != ''">
            and VERSION = #{entity.version}
        </if>
        <if test="entity.lineId != null and entity.lineId != ''">
            and LINE_ID = #{entity.lineId}
        </if>
        <if test="entity.processName != null and entity.processName != ''">
            and PROCESS_NAME like concat(concat('%', #{entity.processName}), '%')
        </if>
        <if test="entity.processNo != null and entity.processNo != ''">
            and PROCESS_NO like concat(concat('%', #{entity.processNo}), '%')
        </if>
        <if test="entity.lockedStatus != null and entity.lockedStatus != ''">
            and LOCKED_STATUS like concat(concat('%', #{entity.lockedStatus}), '%')
        </if>
        ORDER BY business_id DESC
    </select>

    <select id="queryIpmsProductionProcesssPaged" resultMap="BaseResultMap">
        select *
        from dc_ipms_production_process
        where 1 = 1
          and del_flag = '0'
        <if test="entity.flag != null and entity.flag != ''">
            and flag = #{entity.flag}
        </if>
        <if test="entity.processId != null and entity.processId != ''">
            and PROCESS_ID = #{entity.processId}
        </if>
        <if test="entity.version != null and entity.version != ''">
            and VERSION = #{entity.version}
        </if>
        <if test="entity.lineId != null and entity.lineId != ''">
            and LINE_ID = #{entity.lineId}
        </if>
        <if test="entity.processName != null and entity.processName != ''">
            and PROCESS_NAME like concat(concat('%', #{entity.processName}), '%')
        </if>
        <if test="entity.processNo != null and entity.processNo != ''">
            and PROCESS_NO like concat(concat('%', #{entity.processNo}), '%')
        </if>
        <if test="entity.lockedStatus != null and entity.lockedStatus != ''">
            and LOCKED_STATUS like concat(concat('%', #{entity.lockedStatus}), '%')
        </if>
        ORDER BY business_id DESC
    </select>
    <resultMap id="IpmsProProcessOptionVOResultMap"
               type="com.adc.bus.dc.basic.ipms.ipmsproductionprocess.entity.vo.IpmsProProcessOptionVO">
        <result property="processNo" column="PROCESS_NO"/>
        <result property="processName" column="PROCESS_NAME"/>
        <result property="bindProcessId" column="BIND_PROCESS_ID"/>
        <collection property="proProcessOptionVOList"
                    ofType="com.adc.bus.dc.basic.proprocess.entity.vo.ProProcessOptionVO"
                    column="{entity.ipmsProcessCode=PROCESS_NO}"
                    select="com.adc.bus.dc.basic.proprocess.dao.ProProcessRepository.selectProProcessOptionVoList"/>
    </resultMap>
    <select id="selectIpmsProProcessOptionVoList"
            resultMap="IpmsProProcessOptionVOResultMap">
        select ipp, ipp.PROCESS_NAME, pp.BUSINESS_ID BIND_PROCESS_ID
        from DC_IPMS_PRODUCTION_PROCESS ipp
                 left join DC_PRO_PROCESS pp on pp.DEL_FLAG = '0' and pp.IPMS_PROCESS_CODE = ipp.PROCESS_NO
        <where>
            ipp.DEL_FLAG = '0'
        </where>
    </select>
</mapper>