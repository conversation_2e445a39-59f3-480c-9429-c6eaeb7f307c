package com.adc.bus.dc.basic.classesinfo.service.impl;

import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.classesinfo.consumer.ClassesInfoConsumer;
import com.adc.bus.dc.basic.classesinfo.entity.ClassesInfo;
import com.adc.core.util.SnowflakeUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ClassesInfoServiceImplTest extends DCApplicationTest {


    @Autowired
    ClassesInfoServiceImpl classesInfoService;
    @Autowired
    SnowflakeUtil snowflakeUtil;

    @Test
    public void sendDelayMessage() {
        ClassesInfo newestClassInfo = classesInfoService.getById(1907310359942004736L);
        classesInfoService.sendDelayMessage(snowflakeUtil.getSnowFlakedId(), ClassesInfoConsumer.TOPIC, newestClassInfo, true);
    }
}