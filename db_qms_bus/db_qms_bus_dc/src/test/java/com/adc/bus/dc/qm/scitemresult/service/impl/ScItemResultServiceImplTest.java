package com.adc.bus.dc.qm.scitemresult.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.gw.productthroughpointinfo.entity.dto.vo.GetWithLastScannerVO;
import com.adc.bus.dc.qm.scitemresult.entity.ScItemResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertEquals;

public class ScItemResultServiceImplTest extends DCApplicationTest {

    @Autowired
    ScItemResultServiceImpl service;

    @Test
    public void getProductTracebackList_test() {
        Long scItemId = 1907678594008875008L;
        String productId = "DH6NA72250524B0123";
        Long interceptScannerId = 1907617344910524416L;
        List<GetWithLastScannerVO> tracebackList = service.getProductTracebackList(ListUtil.toList(new ScItemResult()
                .setScItemId(scItemId)
                .setProductId(productId)
                .setPlannedExhaustLocation(interceptScannerId)
        ));
        Set<String> productIds = CollStreamUtil.toSet(tracebackList, GetWithLastScannerVO::getProductId);
        assertEquals(CollUtil.newHashSet("DH6NA72250524B0122", "DH6NA72250524B0123", "DH6NA72250524B0120", "DH6NA72250524B0121"), productIds);
    }

    @Test
    public void getProductTracebackList_test_2() {
        Long scItemId = 1907678594008875008L;
        String productId = "DH6NA72250526A0024";
        Long interceptScannerId = 1907617344910524416L;
        List<GetWithLastScannerVO> tracebackList = service.getProductTracebackList(ListUtil.toList(new ScItemResult()
                .setScItemId(scItemId)
                .setProductId(productId)
                .setPlannedExhaustLocation(interceptScannerId)
        ));
        Set<String> productIds = CollStreamUtil.toSet(tracebackList, GetWithLastScannerVO::getProductId);
        assertEquals(CollUtil.newHashSet(
                "DH6NA72250526A0020",
                "DH6NA72250526A0024",
                "DH6NA72250526A0023",
                "DH6NA72250526A0022",
                "DH6NA72250526A0021"
        ), productIds);
    }
}