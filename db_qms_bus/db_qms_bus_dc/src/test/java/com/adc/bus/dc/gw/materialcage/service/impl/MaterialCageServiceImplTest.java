package com.adc.bus.dc.gw.materialcage.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.gw.materialcage.entity.dto.MaterialCageQueryDTO;
import com.adc.bus.dc.gw.materialcage.entity.vo.MaterialCageListVO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Comparator;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class MaterialCageServiceImplTest extends DCApplicationTest {

    @Autowired
    MaterialCageServiceImpl service;

    /**
     * 料笼产品出库 先入先出
     */
    @Test
    public void cageProductCanOutFIFOTest() {
        var code = "DCPA072325";
        List<MaterialCageListVO> materialCageList = service.findAll(new MaterialCageQueryDTO());
        String firstCageCode = materialCageList.stream()
                .filter(it -> ObjUtil.isNotNull(it.getUpStorageTime()))
                .min(Comparator.comparing(MaterialCageListVO::getUpStorageTime))
                .map(MaterialCageListVO::getCode)
                .orElse(null);
        assertEquals(code, firstCageCode);
    }
}