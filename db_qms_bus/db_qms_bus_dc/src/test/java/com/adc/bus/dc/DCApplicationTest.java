package com.adc.bus.dc;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.StrUtil;
import lombok.CustomLog;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.rcisoft.core.jwt.model.CyJwtUser;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

@CustomLog
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DCApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DCApplicationTest {

    @Test
    public void test() {
        System.out.println("Hello World");
        Assert.assertFalse(false);
    }

    @Test
    public void testLog() {
        log.isNotNull(null, "不能为空");
    }

    protected void setUserId() {
        setUserId(1L);
    }

    protected void setUserId(Long userId) {
        SecurityContext context = SecurityContextHolder.getContext();
        context.setAuthentication(new Authentication() {
            @Override
            public Collection<? extends GrantedAuthority> getAuthorities() {
                return List.of();
            }

            @Override
            public Object getCredentials() {
                return null;
            }

            @Override
            public Object getDetails() {
                return null;
            }

            @Override
            public Object getPrincipal() {
                CyJwtUser user = new CyJwtUser(StrUtil.toStringOrNull(userId), null, null, null, false, null, null);
                return user;
            }

            @Override
            public boolean isAuthenticated() {
                return false;
            }

            @Override
            public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {

            }

            @Override
            public String getName() {
                return "";
            }
        });
    }

    @Test
    public void testToMapWithDuplicateKeysByObject() {
        // 定义一个简单的内部类，包含key和sort两个字段
        record Item(String key, int sort) {}
        // 构造包含重复key的对象列表
        List<Item> list = List.of(
            new Item("a", 3),
            new Item("a", 1),
            new Item("b", 2)
        );
        // 使用CollStreamUtil.toMap，key为key字段，value为sort字段
        // 假设toMap的签名为 toMap(Collection<T> list, Function<T, K> keyMapper, Function<T, V> valueMapper)
        Map<String, Integer> result = CollStreamUtil.toMap(list, item -> item.key, item -> item.sort);
        // 检查重复key的处理方式，通常后面的会覆盖前面的
        // 期望结果：{"a":3, "b":2}
        assertEquals(2, result.size());
        assertEquals(Integer.valueOf(1), result.get("a"));
        assertEquals(Integer.valueOf(2), result.get("b"));
    }
}
