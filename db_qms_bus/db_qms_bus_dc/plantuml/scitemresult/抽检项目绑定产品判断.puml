@startuml
'https://plantuml.com/activity-diagram-beta
'产品绑定规则
start

if (即将绑定抽检项目是否切割) then(是)
    if (已绑定抽检项目排出位置是否相同) then(是)
        if (已绑定抽检项目是否切割) then(是)
            if (已绑定抽检项目切割方式是否相同) then(是)
            :绑定;
            else (否)
            :不绑定;
            endif;
        else (否)
        :绑定;
        endif;
    else (否)
    :不绑定;
    endif;
else (否)
    if (已绑定抽检项目是否切割) then(是)
        if (已绑定抽检项目排出位置是否相同) then(是)
        :绑定;
        else (否)
        :不绑定;
        endif;
    else (否)
    :绑定;
    endif;
endif;
stop



@enduml
