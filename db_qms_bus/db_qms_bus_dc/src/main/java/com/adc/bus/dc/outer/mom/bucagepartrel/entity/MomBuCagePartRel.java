package com.adc.bus.dc.outer.mom.bucagepartrel.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

import static com.adc.core.enums.Constant.TIME_FORMAT;

/**
 * 料笼与工件关系表
 */
@Data
@Accessors(chain = true)
@TableName(value = "DHEC_MOM.T_MOM_BU_CAGE_PART_REL", autoResultMap = true)
@ApiModel("料笼与工件关系表")
public class MomBuCagePartRel {

    /**
     * 料笼工件关系ID
     */
    @TableId
    @ApiModelProperty("料笼工件关系ID")
    private String cagePartRelId;

    /**
     * 工件二维码
     */
    @ApiModelProperty("工件二维码")
    private String partQrCode;

    /**
     * 料笼号
     */
    @ApiModelProperty("料笼号")
    private String cageCode;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private BigDecimal orderNo;

    /**
     * 是否可用 1,可用,0不可用
     */
    @ApiModelProperty("是否可用 1,可用,0不可用")
    private String enabled;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = TIME_FORMAT)
    @DateTimeFormat(pattern = TIME_FORMAT)
    private Date createdTime;

    /**
     * 最后更新人员
     */
    @ApiModelProperty("最后更新人员")
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    @ApiModelProperty("最后更新时间")
    @JsonFormat(pattern = TIME_FORMAT)
    @DateTimeFormat(pattern = TIME_FORMAT)
    private Date lastUpdatedTime;

    /**
     * SDP用户ID
     */
    @ApiModelProperty("SDP用户ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @ApiModelProperty("SDP组织ID")
    private String sdpOrgId;

    /**
     * 并发控制字段
     */
    @ApiModelProperty("并发控制字段")
    private String updateControlId;
}
