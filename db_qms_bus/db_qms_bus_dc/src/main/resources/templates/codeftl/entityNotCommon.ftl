package ${basePackage!}.entity;
<#assign tableEntityName = table.entityName[2..]! />
<#list table.columnModels as item>
    <#if item.columnNameLowerCamel!='businessId'&&
    item.columnNameLowerCamel!='remarks'&&
    item.columnNameLowerCamel!='createBy'&&
    item.columnNameLowerCamel!='createDate'&&
    item.columnNameLowerCamel!='updateBy'&&
    item.columnNameLowerCamel!='updateDate'&&
    item.columnNameLowerCamel!='delFlag'&&
    item.columnNameLowerCamel!='flag'>
        <#if item.javaType??>
        <#if item.javaType == "Date">
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
        </#if>
        </#if>
    </#if>
</#list>

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.rcisoft.core.entity.CyIdSnowflakeEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.adc.core.enums.Constant.TIME_FORMAT;

/**
 * ${table.tableRemark?upper_case}
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "${table.tableName!}", autoResultMap = true)
@ApiModel("${table.tableRemark?upper_case}")
public class ${tableEntityName} extends CyIdNotDataEntity<${tableEntityName}> {

    <#assign index = 0, DateRange = 0>
    <#list table.columnModels as item>
        <#if item.columnNameLowerCamel!='businessId'>

     /**
      * ${item.remarks?upper_case}
      */
     @ApiModelProperty("${item.remarks?upper_case}")
            <#if item.javaType?? && item.javaType == "Date">
     @JsonFormat(pattern = TIME_FORMAT)
     @DateTimeFormat(pattern = TIME_FORMAT)
     private ${item.javaType!} ${item.columnNameLowerCamel!};
                <#assign DateRange = 1>
            <#elseif item.javaType?? && item.javaType == "Object">
     @TableField(typeHandler = JacksonTypeHandler.class)
     private ${item.javaType!} ${item.columnNameLowerCamel!};
            <#else >
     private ${item.javaType!} ${item.columnNameLowerCamel!};
            </#if>
            <#assign index = index + 1>
        </#if>
    </#list>
}

