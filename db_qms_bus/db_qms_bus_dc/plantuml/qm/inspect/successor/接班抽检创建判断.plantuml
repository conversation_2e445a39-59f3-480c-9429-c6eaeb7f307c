@startuml
start
if (缺少触发接班抽检信息) then(是)
    stop
else(否)
    :查询产线上正在运行的接班抽检;
    :查询产线上启用的接班抽检模板;
    if (是否有触发日常抽检的接班抽检) then (是)
        if (抽检信息中的班次与触发日常抽检的接班抽检的班次不一致) then (是)
            if (到达需要停止接班抽检的时间) then (是)
                note
                没有启用接班抽检模板
                || 触发日常抽检的接班抽检的班次不一致
                || 到达模板的抽检时间
                end note
                :停止触发日常抽检;
            endif
        endif
    endif
    if (是否有运行的接班抽检) then(是)
        if (抽检信息中的班次与运行中的接班抽检的班次不一致) then (是)
            if (到达需要停止接班抽检的时间) then (是)
                note
                没有启用接班抽检模板
                || 触发日常抽检的接班抽检的班次不一致
                || 到达模板的抽检时间
                end note
                :停止正在进行的接班抽检;
            endif;
        endif
    else (否)
    endif;
    if (是否有正在运行的接班抽检) is (否) then
      if (产线是否启用) then(是)
          if (是否有启用的接班抽检模板) then(是)
             if (抽检信息中的抽检模板与启用的接班抽检单模板一致) then(是)
                  if (抽检时间是否正确) then (是)
                      :接班抽检创建;
                  else (否)
                  endif;
            else (模板被禁用/移动到其他产线)
            endif;
          else (否)
          endif;
      else (否)
      endif;
    else (是)
    endif
    if (产线是否启用) then(是)
      if (是否有启用的接班抽检模板) then(是)
          if (抽检时间是否正确) then (是)
              :发送消息 用于下次接班抽检创建;
          else (否)
          endif;
      else (否)
      endif;
    else (否)
    endif;
stop
@enduml

