#!/bin/bash

# 检查是否提供了路径参数
if [ $# -lt 1 ]; then
    echo "没有提供路径参数，将使用默认路径。"
    targetPath="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/src/type"
else
    targetPath="$1"

    # 检查提供的路径是否以 'src/type' 结尾
    if [[ ! $targetPath == */src/type ]]; then
        # 如果没有，添加 'src/type' 到路径末尾
        targetPath="$targetPath/src/type"
    fi
fi

# 运行 Python 脚本转换 Java 枚举
python ./convertFormTypeEnum.py ../db_qms_common/db_qms_workflow/src/main/java/com/adc/workflow/sysprocessformtype/enums/FormTypeEnum.java ./FormTypeEnum.ts;
python ./convertFormTypeEnum.py ../db_qms_common/db_qms_workflow/src/main/java/com/adc/workflow/sysprocessinstanceapprove/enums/BusinessTypeEnum.java ./BusinessTypeEnum.ts;

# 确保目标路径存在
if [ ! -d "$targetPath" ]; then
    mkdir -p "$targetPath" > /dev/null
fi

# 移动文件到目标路径
mv -f ./FormTypeEnum.ts "$targetPath"
mv -f ./BusinessTypeEnum.ts "$targetPath"

echo "文件已移动到 $targetPath"