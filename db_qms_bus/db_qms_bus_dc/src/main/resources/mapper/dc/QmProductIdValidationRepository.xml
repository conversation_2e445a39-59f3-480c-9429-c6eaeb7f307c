<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.adc.bus.dc.qm.qmproductidvalidation.dao.QmProductIdValidationRepository">
    <sql id="queryProductIdValidationList">
        select piv.BUSINESS_ID,
               piv.PRODUCT_ID,
               cd.DEPT_NAME as department,
               md.NAME      as PROCESS_NAME,
               piv.HOT_MODULE,
               piv.CIRCULATE,
               piv.VALIDATION_TIME,
               piv.PUNCH_LEVEL,
               piv.JUDGE_TIME,
               pl.PROLINE_NAME as PRO_LINE_NAME
        from DC_QM_PRODUCT_ID_VALIDATION piv
                 left join SYS_DEPT cd on cd.DEL_FLAG = '0' and piv.DEPARTMENT_ID = cd.BUSINESS_ID
                 left join DC_MAIN_DEVICE md on md.DEL_FLAG = '0' and md.BUSINESS_ID = piv.PROCESS_ID
                 left join DC_PROLINE_INFO pl on pl.DEL_FLAG = '0' and (pl.BUSINESS_ID = md.PRO_LINE_ID)
        <where>
            piv.DEL_FLAG = '0'
            <if test="queryDTO.prolineId != null and queryDTO.prolineId != ''">
                and piv.PRO_LINE_ID like '%' || #{queryDTO.prolineId} || '%'
            </if>
            <if test="queryDTO.productId != null and queryDTO.productId != ''">
                and piv.PRODUCT_ID like '%' || #{queryDTO.productId} || '%'
            </if>
            <if test="queryDTO.departmentId != null">
                and piv.DEPARTMENT_ID = #{queryDTO.departmentId}
            </if>
            <if test="queryDTO.processId != null">
                and md.BUSINESS_ID = #{queryDTO.processId}
            </if>
            <if test="queryDTO.hotModule != null">
                and piv.HOT_MODULE = #{queryDTO.hotModule}
            </if>
            <if test="queryDTO.circulate != null">
                and piv.CIRCULATE = #{queryDTO.circulate}
            </if>
            <if test="queryDTO.punchLevel != null">
                and piv.PUNCH_LEVEL = #{queryDTO.punchLevel}
            </if>
            <if test="queryDTO.validationTimeStart != null">
                and trunc(piv.VALIDATION_TIME) &gt;= #{queryDTO.validationTimeStart}
            </if>
            <if test="queryDTO.validationTimeEnd != null">
                and trunc(piv.VALIDATION_TIME) &lt;= #{queryDTO.validationTimeEnd}
            </if>
            <if test="queryDTO.ids != null and !queryDTO.ids.isEmpty">
                and piv.BUSINESS_ID in
                <foreach collection="queryDTO.ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY piv.VALIDATION_TIME DESC, piv.BUSINESS_ID DESC
    </sql>

    <!--<cache type="${corePackag!}.util.RedisCache"/>-->
    <select id="queryDcQmProductIdValidations"
            resultType="com.adc.bus.dc.qm.qmproductidvalidation.entity.vo.ProductionIdValidationListVO">
        <include refid="queryProductIdValidationList"/>
    </select>

    <select id="queryDcQmProductIdValidationsPaged"
            resultType="com.adc.bus.dc.qm.qmproductidvalidation.entity.vo.ProductionIdValidationListVO">
        <include refid="queryProductIdValidationList"/>
    </select>

    <select id="queryTotal" resultType="java.lang.Integer">
        select COUNT(BUSINESS_ID)
        from (<include refid="queryProductIdValidationList"/>)
    </select>

    <resultMap id="ProductionIdValidationDetailVOResultMap"
               type="com.adc.bus.dc.qm.qmproductidvalidation.entity.vo.ProductionIdValidationDetailVO">
        <id column="BUSINESS_ID" property="businessId"/>
        <result column="PRODUCT_ID" property="productId"/>
        <result column="OLD_PRODUCT_ID" property="oldProductId"/>
        <result column="PRODUCT_INFO_ID" property="productInfoId"/>
        <result column="PROCESS_CODE" property="processCode"/>
        <result column="PROCESS_NAME" property="processName"/>
        <result column="DEPT_NAME" property="department"/>
        <result column="PRO_LINE_ID" property="proLineId"/>
        <result column="PROLINE_NAME" property="proLineName"/>
        <result column="PRODUCTION_TIME" property="productionTime"/>
        <result column="CLASSES_NAME" property="classes"/>
        <result column="FLOAT_CLASSES_NAME" property="floatClasses" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="ACCEPT_TIME" property="acceptTime"/>
        <result column="HOT_MODULE" property="hotModule"/>
        <result column="HOT_MODULE_SIGNAL" property="hotModuleSignal"/>
        <result column="DUPLICATE" property="duplicate"/>
        <result column="CORRECT" property="correct"/>
        <result column="CIRCULATE" property="circulate"/>
        <result column="VALIDATION_TIME" property="validationTime"/>
        <result column="PUNCH_LEVEL" property="punchLevel"/>
        <result column="QUALIFIED" property="qualified"/>
        <result column="JUDGE_TIME" property="judgeTime"/>
        <result column="FINAL_PUNCH_LEVEL" property="finalPunchLevel"/>
        <result column="FINAL_QUALIFIED" property="finalQualified"/>
        <result column="FINAL_VALIDATION_TIME" property="finalValidationTime"/>
        <collection property="validationResultList"
                    ofType="com.adc.bus.dc.qm.qmproductidvalidationresult.entity.dto.ProductionIdValidationResultBaseDTO"
                    column="BUSINESS_ID"
                    select="com.adc.bus.dc.qm.qmproductidvalidationresult.dao.QmProductIdValidationResultRepository.queryProductionIdValidationResultByValidationId"/>
    </resultMap>
    <select id="selectByBusinessId" resultMap="ProductionIdValidationDetailVOResultMap">
        select piv.BUSINESS_ID,
               piv.PRODUCT_ID,
               piv.OLD_PRODUCT_ID,
               piv.PRODUCT_INFO_ID,
               md.NAME                                          PROCESS_NAME,
               md.CODE                                          PROCESS_CODE,
               cd.DEPT_NAME,
               pi.BUSINESS_ID as                                PRO_LINE_ID,
               pi.PROLINE_NAME,
               piv.PRODUCTION_TIME,
               ci.CLASSES_NAME,
               (select json_arrayagg(CLASSES_NAME)
                from DC_CLASSES_INFO
                where DEL_FLAG = '0'
                  and exists(select 1
                             from json_table(piv.FLOAT_CLASSES_IDS, '$[*]' columns (classes_id number path '$')) j
                             where j.classes_id = BUSINESS_ID)) FLOAT_CLASSES_NAME,
               piv.ACCEPT_TIME,
               piv.HOT_MODULE,
               piv.HOT_MODULE_SIGNAL,
               piv.DUPLICATE,
               piv.CORRECT,
               piv.CIRCULATE,
               piv.VALIDATION_TIME,
               piv.PUNCH_LEVEL,
               piv.QUALIFIED,
               piv.JUDGE_TIME,
               piv.FINAL_PUNCH_LEVEL,
               piv.FINAL_QUALIFIED,
               piv.FINAL_VALIDATION_TIME
        from DC_QM_PRODUCT_ID_VALIDATION piv
                 left join DC_MAIN_DEVICE md on md.DEL_FLAG = '0' and piv.PROCESS_ID = md.BUSINESS_ID
                 left join SYS_DEPT cd on cd.DEL_FLAG = '0' and piv.DEPARTMENT_ID = cd.BUSINESS_ID
                 left join DC_PROLINE_INFO pi on pi.DEL_FLAG = '0' and piv.PRO_LINE_ID = pi.BUSINESS_ID
                 left join DC_CLASSES_INFO ci on ci.DEL_FLAG = '0' and piv.CLASSES_ID = ci.BUSINESS_ID
        <where>
            piv.DEL_FLAG = '0'
              and piv.BUSINESS_ID = #{businessId}
        </where>
    </select>

    <resultMap id="ProductionIdCheckDatabaseInfoDTOResultMap"
               type="com.adc.bus.dc.qm.qmproductidvalidation.entity.dto.ProductionIdCheckDatabaseInfoDTO">
        <result column="SCANNER_ID" property="scannerId"/>
        <result column="SCANNER_NAME" property="scannerName"/>
        <result column="PROCESS_DEVICE_ID" property="processDeviceId"/>
        <result column="PROCESS_DEVICE_NAME" property="processDeviceName"/>
        <result column="PRO_LINE_ID" property="proLineId"/>
        <result column="DEPARTMENT_ID" property="departmentId"/>
        <result column="PROCESS_ID" property="processId"/>
        <result column="DEPARTMENT_NAME" property="departmentName"/>
        <result column="DEVICE_NUMBER" property="castingMachineNumber"/>
        <result column="CASTING_MACHINE_NUMBER" property="castingMachineNo"/>
    </resultMap>

    <select id="queryProductIdCheckDatabaseInfo"
            resultMap="ProductionIdCheckDatabaseInfoDTOResultMap">
        select pds.BUSINESS_ID   SCANNER_ID,
               pds.NAME          SCANNER_NAME,
               dn.BUSINESS_ID    PROCESS_DEVICE_ID,
               dn.NAME           PROCESS_DEVICE_NAME,
               md.BUSINESS_ID    PROCESS_ID,
               mcmn.DEVICE_NUMBER,
               mcmn.CASTING_MACHINE_NUMBER,
               pi.BUSINESS_ID as PRO_LINE_ID,
               pi.CLASS_ID       DEPARTMENT_ID,
               d.DEPT_NAME       DEPARTMENT_NAME
        from DC_PROCESS_DEVICE_SCANNER pds
                 left join DC_DEVICE_NAME dn on dn.DEL_FLAG = '0' and dn.BUSINESS_ID = pds.PROCESS_DEVICE
                 left join DC_MOM_CASTING_MACHINE_NO mcmn on mcmn.CASTING_MACHINE_NUMBER = dn.MOM_CASTING_MACHINE_NO
                 left join DC_MAIN_DEVICE md on md.DEL_FLAG = '0' and md.BUSINESS_ID = dn.MAIN_DEVICE_ID
                 left join DC_PROLINE_INFO pi on pi.DEL_FLAG = '0' and md.PRO_LINE_ID = pi.BUSINESS_ID
                 left join SYS_DEPT d on d.DEL_FLAG = '0' and d.BUSINESS_ID = pi.CLASS_ID
        where pds.DEL_FLAG = '0'
          and pds.CODE = #{scannerCode}
    </select>

    <resultMap id="ProducibleModelInfoResultMap"
               type="com.adc.bus.dc.qm.qmproductidvalidation.entity.dto.ProducibleModelInfo">
        <result column="BUSINESS_ID" property="modelId"/>
        <result column="NAME" property="modelName"/>
        <result column="PART_TYPE" property="partType"/>
        <result column="MODEL_INFO_ID" property="momModelId"/>
        <result column="MODULE_CODE_LIST" property="moduleCodeList"
                javaType="com.adc.bus.dc.qm.qmproductidvalidation.entity.dto.ModuleCodeDTO$ModuleCodeDTOList"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <select id="queryProducibleModelInfo"
            resultMap="ProducibleModelInfoResultMap">
        select mod.BUSINESS_ID,
               mod.NAME,
               mmod.MODEL_INFO_ID,
               mmod.PART_TYPE,
               (select json_arrayagg(json_object('moduleId' : mm.BUSINESS_ID,
                                                 'momModuleId' : m.MODULE_ID,
                                                 'moduleNumber' : mm.VERIFICATION_MOLD_NUMBER))
                from DC_MODULE_MANAGE mm
                         left join DC_MOM_MODULE m on m.DEL_FLAG = '0' and m.MODULE_ID = mm.MOM_MODULE_CODE
                where mm.DEL_FLAG = '0'
                  and exists(select 1
                             from json_table(mmd.MODULE_ID_LIST,
                                             '$[*]'
                                             columns (module_id number path '$')) j
                             where j.module_id = mm.BUSINESS_ID))
                   MODULE_CODE_LIST
        from DC_MODEL_MAIN_DEVICE mmd
                 left join DC_MODEL_MANAGE mod on mod.DEL_FLAG = '0' and mod.BUSINESS_ID = mmd.MODEL_ID
                 left join DC_MOM_MODEL mmod on mmod.DEL_FLAG = '0' and mmod.MODEL_INFO_ID = mod.MOM_MODEL_ID
        where mmd.MAIN_DEVICE_ID = #{processId}
    </select>

    <update id="updateProductId">
        update DC_QM_PRODUCT_ID_VALIDATION
        set OLD_PRODUCT_ID = PRODUCT_ID,
            PRODUCT_ID = #{productId}
        where PRODUCT_INFO_ID = #{productInfoId}
    </update>


</mapper>
