package ${basePackage!}.service.impl;
<#assign tableEntityName = table.entityName[2..]! />

import ${basePackage!}.dao.${tableEntityName}Repository;
import ${basePackage!}.entity.${tableEntityName};
import ${basePackage!}.service.${tableEntityName}Service;
import com.adc.bus.dc.common.service.impl.DSServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.CustomLog;
import org.rcisoft.core.model.CyPageInfo;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * ${table.tableRemark?upper_case} Service Impl
 */
@Service
@CustomLog
public class ${tableEntityName}ServiceImpl extends DSServiceImpl<${tableEntityName}Repository, ${tableEntityName}> implements ${tableEntityName}Service {

    @Override
    public IPage<${tableEntityName}> findAllByPagination(CyPageInfo<${tableEntityName}> paginationUtility, ${tableEntityName} ${table.entityName?uncap_first}) {
        return baseMapper.query${tableEntityName}sPaged(paginationUtility, ${table.entityName?uncap_first});
    }


    @Override
    public List<${tableEntityName}> findAll(${tableEntityName} ${table.entityName?uncap_first}) {
        return baseMapper.query${tableEntityName}s(${table.entityName?uncap_first});
    }
}
