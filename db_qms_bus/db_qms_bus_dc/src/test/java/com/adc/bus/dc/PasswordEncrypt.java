package com.adc.bus.dc;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

public class PasswordEncrypt {
    public static StringEncryptor encryptor;

    static {
        PooledPBEStringEncryptor pbeStringEncryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword("nfei7ur67nfg345srj34HDbvdn");
        config.setAlgorithm("PBEWithHmacSHA512AndAES_128");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        pbeStringEncryptor.setConfig(config);
        encryptor = pbeStringEncryptor;
    }

    public static void main(String[] args) {
        System.out.println(encryptor.encrypt("nswdfq"));
    }
}