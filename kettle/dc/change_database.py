import os
import yaml
import xml.etree.ElementTree as ET
import re
import argparse

# 设置命令行参数解析
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process environment for configuration.')
    parser.add_argument('--env', type=str, help='指定环境 (dev, test, prod).')
    return parser.parse_args()

# 读取配置文件
def load_config(environment):
    with open('application.yml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 如果命令行传入了环境参数，则使用传入的环境
    if environment:
        env_file = f'application-{environment}.yml'
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                env_config = yaml.safe_load(f)
                if env_config:  # 确保 env_config 不是 None
                    config.update(env_config)
                else:
                    print(f"警告: {env_file} 文件可能为空或者不符合yaml格式.")
    else:
        # 默认环境
        environment = config.get('environment', 'default')
        env_file = f'application-{environment}.yml'
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                env_config = yaml.safe_load(f)
                if env_config:  # 确保 env_config 不是 None
                    config.update(env_config)
                else:
                    print(f"警告: {env_file} 文件可能为空或者不符合yaml格式.")

    return config

# 解析命令行参数
args = parse_arguments()
environment = args.env

# 加载配置
config = load_config(environment)
seed = config.get('seed', '0933910847463829827159347601486730416058')
password_prefix = 'Encrypted '

target_info = config['database']['target']
mom_info = config['database']['mom']
output_dir = os.path.join('dist', config.get('output-dir', 'output'))

def parse_url(url):
    match = re.match(r'jdbc:oracle:thin:@//([^:]+):(\d+)(/.+)', url)
    if match:
        ip = match.group(1)
        port = match.group(2)
        db_name = match.group(3)
        return ip, port, db_name
    return None, None, None

def encrypt_password(password):
    bi_passwd = int.from_bytes(password.encode(), 'big')
    bi_seed = int(seed)
    bi_encrypted = bi_seed ^ bi_passwd
    return password_prefix + hex(bi_encrypted)[2:]  # 去掉 '0x' 前缀

def replace_info(file_path, target_info, mom_info, output_dir):
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()

        target_ip, target_port, target_db_name = parse_url(target_info['url'])
        mom_ip, mom_port, mom_db_name = parse_url(mom_info['url'])

        # 替换连接信息
        for connection in root.findall("connection"):
            name = connection.find('name')
            if name is not None:
                if name.text == 'TARGET':
                    connection.find('database').text = target_db_name
                    connection.find('username').text = target_info['username']
                    connection.find('password').text = encrypt_password(target_info['password'])
                    connection.find('server').text = f"//{target_ip}"
                    connection.find('port').text = target_port
                    # 添加 code 为 PORT_NUMBER 的 attribute
                    attributes = connection.find('attributes')
                    if attributes is not None:
                        for attribute in attributes.findall('attribute'):
                            code = attribute.find('code')
                            if code is not None and code.text == 'PORT_NUMBER':
                                attribute.find('attribute').text = target_port
                elif name.text == 'MOM':
                    connection.find('database').text = mom_db_name
                    connection.find('username').text = mom_info['username']
                    connection.find('password').text = encrypt_password(mom_info['password'])
                    connection.find('port').text = mom_port
                    connection.find('server').text = f"//{mom_ip}"
                    # 添加 code 为 PORT_NUMBER 的 attribute
                    attributes = connection.find('attributes')
                    if attributes is not None:
                        for attribute in attributes.findall('attribute'):
                            code = attribute.find('code')
                            if code is not None and code.text == 'PORT_NUMBER':
                                attribute.find('attribute').text = mom_port

        # 替换输出表Schema
        for step in root.findall(".//step"):
            step_type = step.find('type')
            if step_type is not None and step_type.text in ['TableOutput', 'Update', 'InsertUpdate']:
                schema_elem = step.find('.//schema')
                # 根据connection判断需要使用哪个schema
                connection_elem = step.find('.//connection')
                if connection_elem is not None:
                    if connection_elem.text == 'TARGET':
                        schema_elem.text = target_info['username']

        # 另存到指定文件夹
        output_file_path = os.path.join(output_dir, os.path.basename(file_path))
        tree.write(output_file_path, encoding='utf-8', xml_declaration=True)

    except ET.ParseError:
        print(f"Error parsing XML file: {file_path}")
    except Exception as e:
        print(f"An error occurred while processing {file_path}: {e}")

# 创建输出文件夹（如果不存在）
os.makedirs(output_dir, exist_ok=True)

current_dir = os.getcwd()
for filename in os.listdir(current_dir):
    if filename.endswith('.ktr') or filename.endswith('.kjb'):
        file_path = os.path.join(current_dir, filename)
        replace_info(file_path, target_info, mom_info, output_dir)

print("连接信息更新完成!!!!")
