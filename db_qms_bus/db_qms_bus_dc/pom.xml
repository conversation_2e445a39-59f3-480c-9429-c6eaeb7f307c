<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>db_qms_bus</artifactId>
        <groupId>db.qms</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>db_qms_bus_dc</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <org.mapstruct.version>1.6.2</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <liteflow.version>2.12.3</liteflow.version>
        <redisson.version>3.39.0</redisson.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <mssql-jdbc.version>12.8.1.jre11</mssql-jdbc.version>
        <tlog.version>1.5.2</tlog.version>
        <loki-logback-appender.version>0.1.0</loki-logback-appender.version>
        <jtds.version>1.3.1</jtds.version>
        <p6spy.version>3.9.1</p6spy.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>redisson-spring-data-33</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-26</artifactId>
            <version>${redisson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>db.qms</groupId>
            <artifactId>db_qms_core</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.8.1</version>
        </dependency>
        <!-- 链路追踪依赖，引入后可定制skywalking的链路追踪，追踪任意防范并显示请求参数和返回值（使用@Trace注解） -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>9.0.0</version>
        </dependency>
        <dependency>
            <groupId>db.qms</groupId>
            <artifactId>db_qms_util</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>db.qms</groupId>
            <artifactId>db_qms_workflow</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 链路追踪中日志上报需要的依赖 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>9.0.0</version>
        </dependency>
        <!-- Dc springTest 单元测试依赖包   -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${dynamic-datasource.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>${liteflow.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.jtds</groupId>
            <artifactId>jtds</artifactId>
            <version>${jtds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-all-spring-boot-starter</artifactId>
            <version>${tlog.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-logback-appender</artifactId>
            <version>${loki-logback-appender.version}</version>
        </dependency>
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>${p6spy.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!--1.maven install deploy 需要开启以下配置-->
                <!--<excludes>
                    <exclude>*.yml</exclude>
                    <exclude>*.pfx</exclude>
                    <exclude>logback-spring.xml</exclude>
                </excludes>-->
            </resource>

        </resources>

        <plugins>
            <!--2.maven install deploy 需要关闭以下配置-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.5</version>
                <configuration>
                    <mainClass>com.adc.bus.dc.DCApplication</mainClass>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <release>17</release>
                    <source>17</source>
                    <target>17</target>
                    <!--3. maven install deploy 需要开启以下配置-->
                    <excludes>
                        <!--<exclude>**/ProjectApplication.java</exclude>-->
                        <exclude>org/adc/dc/test/**/*.java</exclude>
                    </excludes>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <env>
                        <NACOS_NAMESPACE>--</NACOS_NAMESPACE>
                        <NACOS_ADDR>--</NACOS_ADDR>
                    </env>
                    <baseImage>openjdk:17-jdk</baseImage>
                    <imageName>${project.artifactId}:${project.version}</imageName>
                    <cmd>["java", "-jar", "/root/${project.build.finalName}.jar"]</cmd>
                    <resources>
                        <resource>
                            <targetPath>/root</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>