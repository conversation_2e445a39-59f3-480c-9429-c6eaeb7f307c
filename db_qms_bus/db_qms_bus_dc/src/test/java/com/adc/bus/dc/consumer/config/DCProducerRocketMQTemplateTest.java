package com.adc.bus.dc.consumer.config;

import com.adc.bus.dc.DCApplicationTest;
import org.apache.rocketmq.client.producer.SendResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

public class DCProducerRocketMQTemplateTest extends DCApplicationTest {

    @Autowired
    private DCProducerRocketMQTemplate mqTemplate;

    /**
     * 测试30天消息的发送
     */
    @Test
    public void sendThirtyDayDelayMessage() {
        SendResult result = mqTemplate.syncSendDelayTimeSeconds(
                "test-30-delay",
                "测试消息",
                LocalDateTime.now().until(LocalDateTime.now().plusMinutes(1), ChronoUnit.SECONDS)
        );
        System.out.println(result);
    }
}