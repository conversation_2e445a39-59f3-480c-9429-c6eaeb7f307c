#---------------------自定义配置----------------------------
wx:
  appId: wx111xx
  secret: 506xxx
  timeOut: 3600
jwt:
  header: Authorization
  secret: mySecret
  expiration: 604800
  tokenHead: "Bearer "
  route:
    authentication:
      path: "/login"
      refresh: "/refresh"
      register: "/register"
cy:
  systemName: "qms"
  init:
    admin:
      - "1"
    permission:
      - "44"
      - "51"
      - "52"
      - "53"
    password: 111111
    post_undelete: ["8492d7e67ca64809bb6ded8823659866"]
    user_undelete: ["admin","333e421d32d9425ea99afce95b603902"]
  model:
    permission: rbac
    schema: spbt
    debug: false
    rabbitMq: false
    quartz: false
    redis: true
    mongodb: false
    dbChange: true
    redisCluster: false
    entityParam: true
    # md5
    decryptParam: false
    decryptKey: cyKey
    # sm4
    decryptSm4Param: false
    decryptSm4Secret: 'FFFAAA333777EEEB'
    # 文件存储
    fileStorage:
      model: minIO
      fileTemplate: UUID
    fileUpload: common
    login:
      log: true
      # 1 "", 2 math, 3 char, 4 block,5 word
      captcha: 4
    webService:
      enable: true
    ssl:
      enable: false
    opeLogDb:
      enable: true
      ignorePackage: org.rcisoft.web.sys
      schema: standalone
      addressEnabled: true
    activiti:
      enable: false
      schemaUpdate: true
      databaseType: mysql
    code:
      enable: true
      author: cy
      dbType: oracle
      database: skt1
      basePackage: com.adc.bus.ae
    rejectRepeatSubmit:
      enable: false
      token: ft
      expired: 1800
    xss:
      excludes:
        - "/static/*"
        - "/swagger-resources/*"
        - "/api-docs/*"
        - "/webjars/*"
    access-crl:
      enable: false
      debug: true
      storageModel: fail
      whiteKeyRedis: AccessCrlWhite
      blackKeyRedis: AccessCrlBlack
      whiteList:
        - "************"
        - "************"
      blackList:
    pay:
      twoDiCode:
        apiServer: http://xxx.com
      ali:
        enable: false
        config: /working/resource/ali.properties
      wx:
        enable: false
        config: /working/resource/wx.properties
      acp:
        enable: false
        config: /working/resource/acp.properties
    encryption:
      algorithm: PBEWithHmacSHA512AndAES_128
      keyObtentionIterations: 1000
      poolSize: 1
      providerName: SunJCE
      saltGeneratorClassName: org.jasypt.salt.RandomSaltGenerator
      ivGeneratorClassName: org.jasypt.iv.RandomIvGenerator
      stringOutputType: base64
global:
  path:
    base_upload_location: /working/resource/eduServer/
    base_discovery: 'http://103.131.168.155:22037'
    code_generate_location: /code
    video_location: /video
    temp_location: /temp
    file_location: /file
    images_location: /images
  code:
    admin: ROLE_1001
  resetPassword: 123456
