spring:
  profiles:
    active: dev
  cloud:
    nacos:
      config:
        namespace: dev
        group: dongben
        #添加username password
        username: nacos
        password: nacos
        server-addr: 192.144.239.97:20233
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}
        #添加username password
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: ${spring.cloud.nacos.config.group}
      locator:
        lowerCaseServiceId: true
        enabled: true
  config:
    import:
      - optional:nacos:${spring.application.name}-conf-${spring.profiles.active}.yml?group=${spring.cloud.nacos.config.group}
      - optional:nacos:${spring.application.name}-common.yml?group=${spring.cloud.nacos.config.group}
      - optional:nacos:${spring.application.name}-spbt.yml?group=${spring.cloud.nacos.config.group}
  main:
    allow-bean-definition-overriding: true
  application:
    name: qms-bus-dc

logging:
  level:
    root: info
    com.alibaba.nacos: info