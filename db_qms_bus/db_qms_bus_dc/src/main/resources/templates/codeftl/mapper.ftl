<?xml version="1.0" encoding="UTF-8"?>
<#assign tableEntityName = table.entityName[2..]! />
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${basePackage!}.dao.${tableEntityName}Repository">
    <resultMap id="BaseResultMap" type="${basePackage!}.entity.${tableEntityName}">
    <#list table.columnModels as item>
        <#if item.isKey>
        <id column="${item.colName!}" property="${item.columnNameLowerCamel}"/>
        </#if>
    </#list>

    <#list table.columnModels as item>
        <#if !item.isKey>
        <result column="${item.colName!}" property="${item.columnNameLowerCamel}" <#if item.javaType?? && item.javaType == "Object">typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"</#if> />
        </#if>
    </#list>
    </resultMap>

    <!--<cache type="${r'${corePackag!}'}.util.RedisCache"/>-->
    <select id="query${tableEntityName}s" resultMap="BaseResultMap">
        select * from ${table.tableName}
        where 1=1
        <#if table.idEntity == true>
            and del_flag = '0'
            <if test="entity.flag !=null and entity.flag != '' ">
                and flag = ${r'#{entity.flag}'}
            </if>
        </#if>
        <#list table.columnModels as item>
            <#if item.columnNameLowerCamel!='businessId'&&
                 item.columnNameLowerCamel!='remarks'&&
                 item.columnNameLowerCamel!='createBy'&&
                 item.columnNameLowerCamel!='createDate'&&
                 item.columnNameLowerCamel!='updateBy'&&
                 item.columnNameLowerCamel!='updateDate'&&
                 item.columnNameLowerCamel!='delFlag'&&
                 item.columnNameLowerCamel!='flag'>
                <#if item.javaType == "String">
            <if test="entity.${item.columnNameLowerCamel} !=null and entity.${item.columnNameLowerCamel} != '' ">
                    <#if dbType?lower_case == "oracle">
                and ${item.colName} like concat(concat('%',${r'#{entity.' + item.columnNameLowerCamel + r'}'}),'%')
                    <#else >
                and ${item.colName} like concat('%',${r'#{entity.' + item.columnNameLowerCamel + r'}'},'%')
                    </#if>
            </if>
                <#else >
            <if test="entity.${item.columnNameLowerCamel} !=null">
                and ${item.colName} = ${r'#{entity.' + item.columnNameLowerCamel + r'}'}
            </if>
                </#if>
            </#if>
        </#list>
        ORDER BY business_id DESC
    </select>

    <select id="query${tableEntityName}sPaged" resultMap="BaseResultMap">
        select * from ${table.tableName}
        where 1=1
        <#if table.idEntity == true>
            and del_flag = '0'
            <if test="entity.flag !=null and entity.flag != '' ">
                and flag = ${r'#{entity.flag}'}
            </if>
        </#if>
        <#list table.columnModels as item>
            <#if item.columnNameLowerCamel!='businessId'&&
                 item.columnNameLowerCamel!='remarks'&&
                 item.columnNameLowerCamel!='createBy'&&
                 item.columnNameLowerCamel!='createDate'&&
                 item.columnNameLowerCamel!='updateBy'&&
                 item.columnNameLowerCamel!='updateDate'&&
                 item.columnNameLowerCamel!='delFlag'&&
                 item.columnNameLowerCamel!='flag'>
                <#if item.javaType == "String">
            <if test="entity.${item.columnNameLowerCamel} !=null and entity.${item.columnNameLowerCamel} != '' ">
                    <#if dbType?lower_case == "oracle">
                and ${item.colName} like concat(concat('%',${r'#{entity.' + item.columnNameLowerCamel + r'}'}),'%')
                    <#else >
                and ${item.colName} like concat('%',${r'#{entity.' + item.columnNameLowerCamel + r'}'},'%')
                    </#if>
            </if>
                <#else >
            <if test="entity.${item.columnNameLowerCamel} !=null">
                and ${item.colName} = ${r'#{entity.' + item.columnNameLowerCamel + r'}'}
            </if>
                </#if>
            </#if>
        </#list>
        ORDER BY business_id DESC
    </select>
</mapper>
