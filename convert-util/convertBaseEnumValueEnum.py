import re
import sys
import os

# 检查命令行参数数量
if len(sys.argv) != 2:
    print("Usage: python script.py <path_to_java_enum_file>")
    sys.exit(1)

# 获取Java枚举文件路径
java_enum_file_path = sys.argv[1]

# 读取Java枚举文件内容
try:
    with open(java_enum_file_path, 'r', encoding='utf-8') as f:
        java_enum_content = f.read()
except FileNotFoundError:
    print(f"Error: The file '{java_enum_file_path}' does not exist.")
    sys.exit(1)
except UnicodeDecodeError:
    print(f"Error: Unable to decode the file '{java_enum_file_path}'. Please check the encoding.")
    sys.exit(1)

class_comment = None
class_comment_match = re.compile(r'(/\*\*((?:.|\n)*?)\*/)(?:\s*@\w+.*?)*\s*(public )?enum (\w+)', re.DOTALL).search(java_enum_content)
if class_comment_match:
    class_comment = class_comment_match.group(1).strip()  # 获取类注释内容
# 正则表达式匹配枚举名称和成员描述
enum_name_pattern = re.compile(r"public enum (\w+)")
enum_member_pattern = re.compile(r"\s*(\w+)\(\"(.+?)\"\)", re.MULTILINE)

# 找到枚举名称和成员
enum_name_match = enum_name_pattern.search(java_enum_content)
if not enum_name_match:
    print("Enum name not found.")
    sys.exit(1)
enum_name = enum_name_match.group(1)

enum_members = enum_member_pattern.findall(java_enum_content)

# 转换Java枚举成员为TypeScript枚举成员
ts_enum_members = [f"    /** {desc} */\n    {member.upper()} = '{member}'," for member, desc in enum_members]

# 构建TypeScript枚举字符串
ts_enum_str = f"""// Auto-generated TypeScript enum from Java enum
{class_comment}
export enum {enum_name} {{
"""
for member in ts_enum_members:
    ts_enum_str += member + "\n"
ts_enum_str += "}\n"

# 获取当前工作目录
current_working_directory = os.getcwd()

# 确定TypeScript文件名
ts_file_name = os.path.join(current_working_directory, f"{enum_name}.ts")

# 写入TypeScript枚举到文件
with open(os.path.join(current_working_directory, f"{enum_name}.ts"), 'w', encoding='utf-8') as ts_file:
    ts_file.write(ts_enum_str)

print(f"Generated TypeScript enum: {enum_name}.ts")
