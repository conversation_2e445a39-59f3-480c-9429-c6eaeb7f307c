package com.adc.bus.dc.qm.qmprocessqualitydata.service.impl;

import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.qm.qmprocessqualitydata.service.QmProcessQualityDataService;
import lombok.CustomLog;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

import static org.junit.Assert.*;

@CustomLog
public class QmProcessQualityDataServiceImplTest extends DCApplicationTest {

    @Autowired
    private QmProcessQualityDataService qmProcessQualityDataService;

    @Test
    public void isUnqualified() {
        // 有标准值、最小值 无最大值
        assertFalse(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), new BigDecimal("85"), null, new BigDecimal("85")));
        assertTrue(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), new BigDecimal("85"), null, new BigDecimal("80")));
        // 有标准值、最大值 无最小值
        assertFalse(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), null, new BigDecimal("85"), new BigDecimal("85")));
        assertTrue(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), null, new BigDecimal("85"), new BigDecimal("90")));
        // 有标准值 无最小值、最大值
        assertFalse(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), null, null, new BigDecimal("85")));
        assertTrue(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), null, null, new BigDecimal("90")));
        // 有标准值、最小值、最大值
        assertFalse(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), new BigDecimal("80"), new BigDecimal("90"), new BigDecimal("85")));
        assertTrue(qmProcessQualityDataService.isUnqualified(new BigDecimal("85"), new BigDecimal("80"), new BigDecimal("90"), new BigDecimal("75")));
        // 有最小值、最大值 无标准值
        assertFalse(qmProcessQualityDataService.isUnqualified(null, new BigDecimal("80"), new BigDecimal("90"), new BigDecimal("85")));
        assertTrue(qmProcessQualityDataService.isUnqualified(null, new BigDecimal("80"), new BigDecimal("90"), new BigDecimal("75")));
    }
}