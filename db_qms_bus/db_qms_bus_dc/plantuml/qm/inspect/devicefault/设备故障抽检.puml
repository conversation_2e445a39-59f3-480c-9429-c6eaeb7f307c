@startuml
start
    :MOM发起设备故障抽检;
    :创建“抽检中”的设备故障维修工单;
fork
    :DAP产品过点;
    :查询抽检中的设备故障维修工单;
    if (是发起设备故障后的“首台”过点产品?) is (yes) then
      if (维修完成?) is (yes) then
          else (no)
            :加没有抽检项目的抽检任务;
            :加拦截信息;
            :拦截到产品;
            :扫码;
            while (未完成维修?)
              :等待完成维修;
            endwhile
              :完成维修;
               :查询抽检单模版信息;
              if (需要抽检?) is (yes) then
                  :更新模版ID到设备故障抽检工单;
                else (no)
                  :删除拦截任务;
                  :更新工单为已完成;
                  stop
                endif
              :再次扫码;
              :查询抽检中没有维修完成时间的故障工单（根据MOM工单号）;
              :补充QMS维修完成时间;
          endif
    else (no)
      :首台抽检绑定产品;
    endif

fork again
    :MOM完成设备维修;
    :查询抽检单模版信息;
    if (需要抽检?) is (yes) then
      :更新模版ID到设备故障抽检工单;
    else (no)
      :删除没有抽检项目的抽检任务;
      :删除拦截任务;
       :更新工单为已完成;
    stop
    endif
    if (已经有“首台”产品过点?) is (yes) then
      :等待补充QMS完成维修时间;
    else (no)
    endif

end merge
:首末台绑定产品;
repeat
    :录入抽检结果;
    repeat while(未完成全部抽检？)
  :更新工单为已完成;
  stop


@enduml