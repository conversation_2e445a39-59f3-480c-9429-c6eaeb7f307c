package com.adc.bus.dc.basic.devicename.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.adc.bus.dc.DCApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

import static org.junit.Assert.*;

public class DeviceNameServiceImplTest extends DCApplicationTest {

    @Autowired
    DeviceNameServiceImpl service;

    @Test
    public void getOutletPositionByProcessDeviceId() {
        Set<Long> outletPosition = service.getOutletPositionByProcessDeviceId(1907380288523337730L);
        assertEquals(CollUtil.newHashSet(1907615594187063296L), outletPosition);
    }
}