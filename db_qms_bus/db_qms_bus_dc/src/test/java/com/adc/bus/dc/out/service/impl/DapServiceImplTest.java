package com.adc.bus.dc.out.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ConcurrencyTester;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.adc.bus.dc.DCApplicationTest;
import lombok.CustomLog;
import org.junit.Test;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;

import static com.adc.bus.dc.out.service.impl.DapServiceImpl.DC_PRODUCT_ID_SERIAL_NUMBER_KEY_TEMPLATE;
import static org.junit.Assert.assertEquals;

@CustomLog
public class DapServiceImplTest extends DCApplicationTest {

    @Resource
    DapServiceImpl service;

    @Resource
    RedissonClient redissonClient;


    @Test
    public void generateNewSerialNumber() {
        long processDeviceId = 1907380288519143426L;
        assertEquals("0010", service.generateNewSerialNumber("DB59BA6250414A0010", processDeviceId));
        assertEquals("0011", service.generateNewSerialNumber("DB59BA6250414A0018", processDeviceId));
        RBucket<Long> bucket = redissonClient.getBucket(StrUtil.format(DC_PRODUCT_ID_SERIAL_NUMBER_KEY_TEMPLATE,
                MapUtil.builder()
                        .put("procesDeviceId", processDeviceId)
                        .put("productIdPrefix", "DB59BA6A")
                        .build()));
        log.debug("bucket.get(): {}", bucket.get());
        bucket.delete();
    }


    @Test
    public void generateNewSerialNumber_async() throws IOException {
        long processDeviceId = 1907380288519143426L;
        AtomicInteger count = new AtomicInteger(0);
        try (ConcurrencyTester concurrencyTester = ThreadUtil.concurrencyTest(10, () -> {
            int i = count.getAndIncrement();
            log.debug("i: {}", i);
            assertEquals(String.format("%04d", i), service.generateNewSerialNumber("DB59BA6250414A0010", processDeviceId));
        })) {
            long interval = concurrencyTester.getInterval();
            log.info("interval: {}ms", interval);
            RBucket<Long> bucket = redissonClient.getBucket(StrUtil.format(DC_PRODUCT_ID_SERIAL_NUMBER_KEY_TEMPLATE,
                    MapUtil.builder()
                            .put("procesDeviceId", processDeviceId)
                            .put("productIdPrefix", "DB59BA6A")
                            .build()));
            log.debug("bucket.get(): {}", bucket.get());
            bucket.delete();
        }

    }
}