package com.adc.bus.dc.cp.makecondition.service.impl;

import com.adc.bus.dc.DCApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.Assert.*;

public class ChangePointMakeConditionServiceImplTest extends DCApplicationTest {

    @Autowired
    ChangePointMakeConditionServiceImpl service;

    @Test
    @Transactional
    public void manualClose() {
        setUserId();
        service.manualClose(1910142819071426560L);
    }
}