server:
  port: 8021
  tomcat:
    max-threads: 300
  servlet:
    context-path: /
    session:
      timeout: PT480M
  #ssl:
  #  key-store: classpath:fy.pfx
  #  key-store-password: cy123456
  #  key-store-type: PKCS12

mybatis-plus:
  mapper-locations: "classpath*:mapper/**/**/*.xml"
  global-config:
    db-config:
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

# ------------spring component------------------
spring:
  main:
    allow-bean-definition-overriding: true
  multipart:
    max-file-size: 100Mb
    max-request-size: 1000Mb
  mvc:
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ant-path-matcher
  freemarker:
    charset: UTF-8
    suffix: .ftl
    template-loader-path: classpath:/templates/
  # 1. rabbitmq ------
  rabbitmq:
    addresses: ${cy_mq.ip}:${cy_mq.port}
    username: ${cy_mq.username}
    password: ${cy_mq.password}
    virtual-host: /
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true #是否开启重试
          initial-interval: 3000ms #重试时间间隔
          max-attempts: 3 #重试次数
          max-interval: 15000ms #重试最大时间间隔
          multiplier: 2 #倍数
  # 2. redis ------
  redis:
    host: ${cy_redis.ip}
    port: ${cy_redis.port}
    database: ${cy_redis.database:0}
    password: ${cy_redis.password}
    timeout: 3000
    jedis:
      pool:
        max-active:  32
        max-idle: 16
        max-wait: 300ms
        min-idle: 8

  # 3. redis-cluster ------
  #  redis:
  #    database: 0
  #    timeout: PT2M
  #    lettuce:
  #      cache:
  #        database: 0
  #        timeout: 2000 #客户端超时时间单位是毫秒 默认是2000
  #        maxIdle: 10 #最大空闲数
  #        minIdle: 10 #最xiao空闲数
  #        maxTotal: 40 #控制一个pool可分配多少个jedis实例,用来替换上面的redis.maxActive,如果是jedis 2.4以后用该属性
  #        maxWaitMillis: 4000 #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
  #        minEvictableIdleTimeMillis: 300000 #连接的最小空闲时间 默认1800000毫秒(30分钟)
  #        numTestsPerEvictionRun: 1024 #每次释放连接的最大数目,默认3
  #        timeBetweenEvictionRunsMillis: 30000 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
  #        testOnBorrow: true #是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
  #        testWhileIdle: true #在空闲时检查有效性, 默认false
  #        commandTimeout: 2000
  #        maxRedirects: 6
  #        clusterNodes: ${redisIp}:6391,${redisIp}:6393,${redisIp}:6396,${redisIp}:6394,${redisIp}:6392,${redisIp}:6395
  #        password: '098765'
  #      pool:
  #        max-active: 20
  #        max-idle: 10
  #        min-idle: 10
  #        max-wait: -1s

  # 4. mongodb
  data:
    mongodb:
      uri: mongodb://${cy_mongodb.username}:${cy_mongodb.password}@${cy_mongodb.ip}:${cy_mongodb.port}/${cy_mongodb.db}?authSource=${cy_mongodb.authDb}
      transactionEnabled: true

  # 5. rdbms
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 50
      maximum-pool-size: 200
      idle-timeout: 20000
      pool-name: DatebookHikariCP
      max-lifetime: 180000
      connection-timeout: 3000
      # mysql
      connection-test-query: select 1 from dual

    #---------5.1 MYSQL-------------
    # mysql5.0
    #driver-class-name: com.mysql.jdbc.Driver
    #driver-class-name: com.mysql.cj.jdbc.Driver
    #url: jdbc:mysql://${cy_db.ip}:${cy_db.port}/${cy_db.db}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&allowMultiQueries=true&serverTimezone=GMT%2B8
    #username: ${cy_db.username}
    #password: ${cy_db.password}

    #---------5.2 ORACLE-------------
    driver-class-name: oracle.jdbc.OracleDriver
    url: jdbc:oracle:thin:@${cy_db.ip}:${cy_db.port}/${cy_db.db}
    username: ${cy_db.username}
    password: ${cy_db.password}

knife4j:
  markdowns: classpath:markdown/*
  basic:
    enable: false
    username: zhangsan
    password: 123456


springfox:
  documentation:
    swagger:
      v2:
        path: /api-docs

# 滑块验证码
aj:
  captcha:
    # 滑块底图路径
    #jigsaw: classpath:images/jigsaw
    # 点选底图路径
    #pic-click: classpath:images/pic-click

    # 缓存类型 redis/local
    cache-type: redis
    # blockPuzzle 滑块 clickWord 文字点选  default默认两者都实例化
    type: blockPuzzle
    # 右下角显示字
    water-mark: 91isoft-spbt
    # 校验滑动拼图允许误差偏移量(默认5像素)
    slip-offset: 5
    # aes加密坐标开启或者禁用(true|false)
    aes-status: true
    # 滑动干扰项(0/1/2)
    interference-options: 2
    #点选字体样式 默认Font.BOLD
    aj.captcha.font-style: 1
    #点选字体字体大小
    aj.captcha.font-size: 25
    #点选文字个数,存在问题，暂不支持修改
    #aj.captcha.click-word-count=4
    # 接口请求次数一分钟限制是否开启 true|false
    #aj.captcha.req-frequency-limit-enable: false
    # 验证失败5次，get接口锁定
    #aj.captcha.req-get-lock-limit: 5
    # 验证失败后，锁定时间间隔,s
    #aj.captcha.req-get-lock-seconds: 360
    # get接口一分钟内请求数限制
    aj.captcha.req-get-minute-limit: 30
    # check接口一分钟内请求数限制
    aj.captcha.req-check-minute-limit: 60
    # verify接口一分钟内请求数限制
    aj.captcha.req-verify-minute-limit: 60
