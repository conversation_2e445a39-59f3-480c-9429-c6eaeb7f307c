package com.adc.bus.dc.basic.commonsetting.service.impl;

import com.adc.bus.dc.DCApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class CommonSettingServiceImplTest extends DCApplicationTest {

    @Autowired
    private CommonSettingServiceImpl commonSettingService;

    @Test
    public void getPeerQualityGate() {
        List<Long> peerQualityGate = commonSettingService.getPeerQualityGate(1854470191518973952L);
        System.out.println(peerQualityGate);
    }
}