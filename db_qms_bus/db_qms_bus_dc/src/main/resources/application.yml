management:
  endpoints:
    web:
      exposure:
        include: '*'
spring:
  profiles:
    active: ${SPRING_ACTIVE:dev}
  cloud:
    nacos:
      config:
        namespace: ${NACOS_NAMESPACE:db-qms}
        group: dongben
        #添加username password
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nswdfq}
        server-addr: ${NACOS_ADDR:localhost:8848}
        refresh-enabled: false
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}
        #添加username password
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: ${spring.cloud.nacos.config.group}
      locator:
        lowerCaseServiceId: true
        enabled: true
  config:
    import:
      - optional:nacos:${spring.application.name}-conf-${spring.profiles.active}.yml?group=${spring.cloud.nacos.config.group}
      - optional:nacos:${spring.application.name}-common.yml?group=${spring.cloud.nacos.config.group}
      - optional:nacos:${spring.application.name}-spbt.yml?group=${spring.cloud.nacos.config.group}
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yml?group=${spring.cloud.nacos.config.group}
  main:
    allow-bean-definition-overriding: true
  application:
    name: qms-bus-dc

logging:
  level:
    root: info
    com.alibaba.nacos: info
mybatis-plus:
  configuration:
    local-cache-scope: statement
    cache-enabled: false
    return-instance-for-empty-row: true

