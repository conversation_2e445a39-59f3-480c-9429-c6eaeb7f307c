package com.adc.bus.dc.qm.qmproductidvalidation.service.impl;

import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.mom.mommodule.entity.vo.MomModuleOptionVO;
import lombok.CustomLog;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

@CustomLog
public class QmProductIdValidationServiceImplTest extends DCApplicationTest {

    @Resource
    private QmProductIdValidationServiceImpl service;


    @Test
    public void getUpMomModule_prod() {
        List<MomModuleOptionVO> upMomModule = service.getUpMomModule(1892017056061325312L);
        log.info("上模信息: {}", upMomModule);
    }

    @Test
    public void getUpModelInfo_prod() {
        service.getUpModelInfo(service.getUpMomModule(1892017056061325312L));
    }
}