package com.adc.bus.dc.basic.processdevicescanner;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.adc.bus.dc.DCApplicationTest;
import com.adc.bus.dc.basic.devicename.entity.DeviceName;
import com.adc.bus.dc.basic.devicename.service.DeviceNameService;
import com.adc.bus.dc.basic.maindevice.entity.MainDevice;
import com.adc.bus.dc.basic.maindevice.service.MainDeviceService;
import com.adc.bus.dc.basic.processdevicescanner.dao.ProcessDeviceScannerRepository;
import com.adc.bus.dc.basic.processdevicescanner.entity.ProcessDeviceScanner;
import com.adc.bus.dc.basic.processdevicescanner.entity.dto.ScannerQueryDTO;
import com.adc.bus.dc.basic.processdevicescanner.service.ProcessDeviceScannerService;
import com.adc.bus.dc.basic.prolineinfo.entity.ProlineInfo;
import com.adc.bus.dc.basic.prolineinfo.service.ProlineInfoService;
import com.adc.core.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;

@Slf4j
public class TestDataAdd extends DCApplicationTest {

    @Autowired
    private ProlineInfoService prolineInfoService;

    @Autowired
    private MainDeviceService processService;

    @Autowired
    private DeviceNameService processDeviceService;

    @Autowired
    private ProcessDeviceScannerService processDeviceScannerService;

    @Autowired
    private SnowflakeUtil snowflakeUtil;

    @Autowired
    private ProcessDeviceScannerRepository scannerRepository;

    @Test
    public void queryScannerOption() {
        TimeInterval timer = DateUtil.timer();
        scannerRepository.selectOption(new ScannerQueryDTO().setBeforeScannerIdList(ListUtil.toList(1859943068338880513L,
                1859943068376629252L
        )));
        log.info("查询时间: {}ms", timer.interval());
    }

    @Test
    @Transactional(rollbackFor = Exception.class)
    public void proLineProcessProcessDeviceProcessDeviceScannerAdd() {
        String flag = "TRUE";
        ProlineInfo line = new ProlineInfo();
        line.setBusinessId(snowflakeUtil.getSnowFlakedId());
        line.setProlineName("yangao测试用产线");
        line.setDeptId("2");
        line.setShiftType(ListUtil.toList(1829024824983945216L, 1830485505499332608L));
        line.setFlag(flag);
        MainDevice baseProcess = new MainDevice();
        baseProcess.setName("工序{}");
        baseProcess.setFlag(flag);
        baseProcess.setProLineId(line.getBusinessId());
        DeviceName baseProcessDevice = new DeviceName();
        baseProcessDevice.setName("工序设备{}");
        baseProcessDevice.setFlag(flag);
        ProcessDeviceScanner baseScanner = new ProcessDeviceScanner();
        baseScanner.setName("扫码位置{}");
        baseScanner.setFlag(flag);
        baseScanner.setMaxTimes(1L);
        baseScanner.setFeedRobot(false);
        baseScanner.setNg(false);
        // 产线信息
        /*
        一级: 同级工序列表
        二级: 工序列表
        三级: 工序设备
        四级（值）: 扫码位置 0非NG 1NG
         */
        int[][][][] structWeight = new int[][][][]{
                {{{0}, {0, 0, 1}}, {{0}, {0, 0, 1}}},
                {{{0, 0, 0, 1}}},
                {{{0, 1}}, {{0, 1}}, {{0, 1}}},
                {{{0, 1}}},
                {{{0, 1}}},
        };
        ArrayList<MainDevice> processEntityList = ListUtil.toList();
        ArrayList<DeviceName> processDeviceEntityList = ListUtil.toList();
        ArrayList<ProcessDeviceScanner> processDeviceScannerEntityList = ListUtil.toList();
        int number = 2000;
        for (int j = 0; j < number; j++) {
            for (int i = 0; i < structWeight.length; i++) {
                var processList = structWeight[i];
                for (int i1 = 0; i1 < processList.length; i1++) {
                    MainDevice process = BeanUtil.copyProperties(baseProcess, MainDevice.class);
                    String code = String.format("%03d%04d%04d", i + 1, i1 + 1, j + 1);
                    process.setBusinessId(snowflakeUtil.getSnowFlakedId());
                    process.setName(StrUtil.format(process.getName(), code));
                    process.setCode(code);
                    process.setWeight(i + 1);
                    processEntityList.add(process);
                    int[][] processDeviceList = processList[i1];
                    for (int i2 = 0; i2 < processDeviceList.length; i2++) {
                        DeviceName processDevice = BeanUtil.copyProperties(baseProcessDevice, DeviceName.class);
                        String code1 = String.format("%s%03d", code, i2 + 1);
                        processDevice.setBusinessId(snowflakeUtil.getSnowFlakedId());
                        processDevice.setName(StrUtil.format(processDevice.getName(), code1));
                        processDevice.setAbbreviation(code1);
                        processDevice.setMainDeviceId(process.getBusinessId());
                        processDevice.setWeight(i2 + 1L);
                        processDeviceEntityList.add(processDevice);
                        int[] processDeviceScannerList = processDeviceList[i2];
                        for (int i3 = 0; i3 < processDeviceScannerList.length; i3++) {
                            String code2 = String.format("%s%03d", code1, i3 + 1);
                            ProcessDeviceScanner scanner = BeanUtil.copyProperties(baseScanner, ProcessDeviceScanner.class);
                            scanner.setBusinessId(snowflakeUtil.getSnowFlakedId());
                            scanner.setProcessDevice(processDevice.getBusinessId());
                            scanner.setName(StrUtil.format(scanner.getName(), code2));
                            scanner.setCode(code2);
                            scanner.setNg(NumberUtil.isOdd(processDeviceScannerList[i3]));
                            scanner.setWeight(i3 + 1L);
                            processDeviceScannerEntityList.add(scanner);
                        }
                    }
                }
            }
        }
        log.debug("line: {}", line);
        log.debug("processEntityList: {}", processEntityList);
        log.debug("processDeviceEntityList: {}", processDeviceEntityList);
        log.debug("processDeviceScannerEntityList: {}", processDeviceScannerEntityList);
        prolineInfoService.save(line);
        processService.saveBatch(processEntityList);
        processDeviceService.saveBatch(processDeviceEntityList);
        processDeviceScannerService.saveBatch(processDeviceScannerEntityList);
        TimeInterval timer = DateUtil.timer();
        scannerRepository.selectOption(new ScannerQueryDTO().setBeforeScannerIdList(ListUtil.toList(1859943068338880513L,
                1859943068376629252L
        )));
        log.info("{}数据量查询时间: {}ms", number * 22, timer.interval());
    }
}
