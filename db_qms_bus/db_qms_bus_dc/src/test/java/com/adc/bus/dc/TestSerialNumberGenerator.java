package com.adc.bus.dc;

import cn.hutool.core.stream.CollectorUtil;
import cn.hutool.core.util.ObjUtil;
import com.adc.bus.dc.basic.scitem.enums.ScItemCutTypeEnum;
import com.adc.bus.dc.cp.generator.SerialNumberGenerator;
import com.adc.bus.dc.qm.qmmodulefault.enums.InspectFailTypeEnum;
import com.adc.bus.dc.qm.scitemresult.entity.ScItemResult;
import com.adc.bus.dc.qm.scitemresult.service.ScItemResultService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertTrue;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestSerialNumberGenerator {


    @Resource
    private ScItemResultService scItemResultService;
    @Test
    public void activeScItemResult_StatusFalse_RecordUpdated() {
        Long cpBusinessId = 1874704875326275584L;
        InspectFailTypeEnum cpType = InspectFailTypeEnum.CP_DEVICE;

        boolean result = scItemResultService.activeScItemResult(cpBusinessId, cpType);

        assertTrue(result);
    }

    @Test
    public void test() {
        List<ScItemResult> list = List.of(new ScItemResult().setCutType(ScItemCutTypeEnum.VERTICAL), new ScItemResult().setCutType(ScItemCutTypeEnum.CROSS), new ScItemResult(), new ScItemResult());
        Map<ScItemCutTypeEnum, List<ScItemResult>> needInspectCutMap = list.stream()
                .collect(CollectorUtil.groupingBy(ScItemResult::getCutType, Function.identity()));
        System.out.println(needInspectCutMap);
    }
    @Test
    public void test111() {
        Runnable methodA = () -> {
            String serialNumber = SerialNumberGenerator.generateSerialNumber("BHMJ", "BHMJ20240929001");
            System.out.println("Method A generated serial number: " + serialNumber);
        };

        Runnable methodB = () -> {
            String serialNumber = SerialNumberGenerator.generateSerialNumber("BHMJ", "BHMJ20240929001");
            System.out.println("Method B generated serial number: " + serialNumber);
        };

        // 创建多个线程并发执行
        Thread[] threads = new Thread[10];
        for (int i = 0; i < 10; i++) {
            if (i % 2 == 0) {
                threads[i] = new Thread(methodA);
            } else {
                threads[i] = new Thread(methodB);
            }
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程执行完毕
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}