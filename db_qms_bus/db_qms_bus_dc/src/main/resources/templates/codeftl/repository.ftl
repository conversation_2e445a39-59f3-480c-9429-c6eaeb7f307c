package ${basePackage!}.dao;
<#assign tableEntityName = table.entityName[2..]! />

import ${basePackage!}.entity.${tableEntityName};
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.rcisoft.core.mapper.CyBaseMapper;
import org.rcisoft.core.model.CyPageInfo;

import java.util.List;

/**
 * ${table.tableRemark?upper_case} Mapper
 */
public interface ${tableEntityName}Repository extends CyBaseMapper<${tableEntityName}> {

    /**
     * 列表查询
     */
    List<${tableEntityName}> query${tableEntityName}s(@Param("entity") ${tableEntityName} ${tableEntityName?uncap_first});

    /**
     * 分页查询
     */
    IPage<${tableEntityName}> query${tableEntityName}sPaged(CyPageInfo<${tableEntityName}> cyPageInfo, @Param("entity") ${tableEntityName} ${tableEntityName?uncap_first});
}

