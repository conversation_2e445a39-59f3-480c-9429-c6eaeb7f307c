@startuml

start
:接收MOM过点数据;

switch (扫码位置)
case (含浸)
    if (产品是否为含浸品) then (是)
        note right
        获取产品是否在后处理产品列表中并且是MC创建数据
        end note
        :记录异常（产品非含浸品）;
        stop
    endif
    if (产品泄露类型) then (大漏)
        if (是否有压检数据) then (否)
            :记录异常（大漏产品未经过压检工序）;
        endif
    endif
    if (产品含浸次数) then (> 2)
        :记录异常（产品加工次数异常）;
    endif
    :存储含浸过点、工艺数据,修改后处理产品状态为处理中（含浸加工次数 +1）;
case (压检)
    if (产品是否为含浸品) then (是)
        note right
        获取产品是否在后处理产品列表中并且是MC创建数据
        end note
        :记录异常（产品非含浸品）;
        stop
    endif
    if (产品泄露类型) then (小漏)
        if (是否有含浸数据) then (否)
            :记录异常（小漏产品未经过含浸工序）;
        endif
    endif
    :存储压检过点、工艺数据, 修改后处理产品状态为处理中;
case (绑笼（压检下线）)
    if (产品报废) then (是)
        :将后处理产品状态修改为报废;
    endif
    if (产品无异常记录&&产品同时经过压检与含浸) then (true)
        :向MC发送后处理成功的产品ID;
        :将后处理产品状态修改重新上线;
    else (false)
        :向MOM推送异常（产品流动异常）;
    endif
    :向MC发送产品信息处理;
endswitch

stop

@enduml
