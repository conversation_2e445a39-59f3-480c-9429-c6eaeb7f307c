package com.adc.bus.dc;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.adc.bus.dc.basic.libr.service.LibrSqlServerService;
import com.adc.bus.dc.gw.productthroughpointinfo.entity.ProductThroughPointInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DCApplication.class)
public class TestDateTime {

    @Mock
    private LibrSqlServerService librSqlServerServiceMock;

    @Test
    public void test1() {
        List<ProductThroughPointInfo> productThroughPointInfos = new ArrayList<>();
        productThroughPointInfos.add(new ProductThroughPointInfo().setThroughTime(new Date()));
        productThroughPointInfos.add(new ProductThroughPointInfo().setThroughTime(DateUtil.offsetHour(new Date(), 1)));
        Date th = productThroughPointInfos.stream().map(ProductThroughPointInfo::getThroughTime).min((a, b) -> CompareUtil.compare(b, a, true)).get();
        System.out.println(th);
    }

    @Test
    public void getCageUpperToTime_EmptyCageNo_ReturnsNull() {
        String cageNo = "DC230090";

        // 模拟方法行为
        Date date = librSqlServerServiceMock.getCageUpperToTime(cageNo);

       Date actualDate = librSqlServerServiceMock.getCageUpperToTime(cageNo);

    }

    /**
     * 测试hutool的时间方法
     */
    @Test
    public void getDateNowByHutool() {
        DateTime dateTime = DateUtil.date();
        long time = dateTime.getTime();
        System.out.println("hutool获取当前时间信息：" + dateTime +  "时间毫秒值为" +time);
        System.out.println("Java自带信息确定信息：" + new Date());
        int compare1 = DateUtil.compare(new Date(time), new Date(time + 2 * 1000));
        int compare2 = DateUtil.compare(new Date(time), new Date(time));
        int compare3 = DateUtil.compare(new Date(time+2*1000), new Date(time));
        System.out.println("1号位小于2号位=" + compare1);// -1
        System.out.println("1号位=2号位=" + compare2);//0
        System.out.println("1号位大于2号位=" + compare3);//1
    }

    /**
     * 测试今天、明天的LocalTime判断
     *
     * 如果需要直接看结果，可以不用运行，最终打印结果如下:
     * yesTime < startTime ? = false
     * nowTime < startTime ? = true
     *
     * 结论: 根据源码亦可判断，其实原理是根据Integer.compare对小时和分钟进行比较的
     * 所以不会判断日期是今天还是昨天的时间
     */
    @Test
    public  void testLocalTimeWithNow(){
        LocalTime thresholdStart = LocalTime.of(7, 30);

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesDateTime = LocalDateTime.of(now.toLocalDate().minusDays(1), LocalTime.of(23, 0));
        LocalDateTime nowDateTime = LocalDateTime.of(now.toLocalDate(), LocalTime.of(7, 29));

        // 获取当前时间
        // 定义7:30和7:29的时间
        System.out.println("yesTime < startTime ? = " + yesDateTime.toLocalTime().isBefore(thresholdStart));
        System.out.println("nowTime < startTime ? = " + nowDateTime.toLocalTime().isBefore(thresholdStart));
    }

    @Test
    public void testLocalDateTimeBetweenData(){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTime.of(
                now.toLocalDate()
                        .minusDays(1)
                        .minusDays(7),
                LocalTime.of(7, 30)
        );
        LocalDateTime endTime = LocalDateTime.of(
                now.toLocalDate()
                        .minusDays(1),
                LocalTime.of(7, 30)
        );
        long days = DateUtil.betweenDay(
                Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()),
                true);
        System.out.println("开始时间与结束时间的相差天数为: " +days);
    }

    public static void main(String[] args) {
        // 测试时间转换
        DateTime parse = DateUtil.parse("07:00");
        DateTime today = DateUtil.beginOfDay(DateUtil.date());
        long betweenS = DateUtil.between(today, parse, DateUnit.SECOND);
    }
}
